#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android Ramdisk CPIO 命令行管理工具
Copyright 2025 By.举个🌰

简化的命令行界面工具，用于管理ramdisk.cpio文件
"""

import os
import sys
import shutil
import subprocess
import hashlib
from pathlib import Path
from datetime import datetime
from cpio_handler import CPIOHandler

class RamdiskCLI:
    def __init__(self):
        self.work_dir = Path.cwd()
        self.cpio_file = self.work_dir / "recovery" / "ramdisk.cpio"
        self.extract_dir = self.work_dir / "ramdisk_extracted"
        self.backup_dir = self.work_dir / "backup"
        self.cpio_handler = CPIOHandler()
        
    def print_header(self):
        """打印程序头部信息"""
        print("=" * 60)
        print("Android Ramdisk CPIO 命令行管理工具")
        print("Copyright 2025 By.举个🌰")
        print("=" * 60)
        print()
        
    def print_status(self):
        """打印当前状态"""
        print(f"📁 工作目录: {self.work_dir}")
        print(f"📄 CPIO文件: {self.cpio_file}")
        print(f"📂 解包目录: {self.extract_dir}")
        print(f"💾 备份目录: {self.backup_dir}")
        print()
        
        # 检查文件状态
        cpio_exists = self.cpio_file.exists()
        extract_exists = self.extract_dir.exists()
        
        print("📊 文件状态:")
        print(f"  CPIO文件: {'✓ 存在' if cpio_exists else '❌ 不存在'}")
        print(f"  解包目录: {'✓ 存在' if extract_exists else '❌ 不存在'}")
        
        if cpio_exists:
            stat = self.cpio_file.stat()
            size_mb = stat.st_size / 1024 / 1024
            print(f"  文件大小: {stat.st_size:,} 字节 ({size_mb:.2f} MB)")
            
        if extract_exists:
            try:
                files = list(self.extract_dir.rglob('*'))
                print(f"  解包文件数: {len(files)}")
            except:
                print(f"  解包文件数: 无法统计")
        print()
        
    def calculate_hash(self, file_path):
        """计算文件MD5哈希"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"❌ 计算哈希失败: {e}")
            return None
            
    def create_backup_dir(self):
        """创建备份目录"""
        if not self.backup_dir.exists():
            self.backup_dir.mkdir()
            print(f"✓ 创建备份目录: {self.backup_dir}")
            
    def extract_cpio(self):
        """解包CPIO文件"""
        print("📦 开始解包CPIO文件...")
        
        if not self.cpio_file.exists():
            print("❌ CPIO文件不存在")
            return False
            
        # 创建备份
        self.create_backup_dir()
        backup_file = self.backup_dir / f"{self.cpio_file.name}.backup"
        if not backup_file.exists():
            shutil.copy2(self.cpio_file, backup_file)
            print(f"✓ 创建备份: {backup_file.name}")
        
        # 计算原始哈希
        original_hash = self.calculate_hash(self.cpio_file)
        if original_hash:
            print(f"📋 原始文件MD5: {original_hash}")
        
        # 删除现有解包目录
        if self.extract_dir.exists():
            print(f"🗑️ 删除现有目录: {self.extract_dir}")
            shutil.rmtree(self.extract_dir)
        
        self.extract_dir.mkdir(parents=True)
        
        # 尝试使用7-zip解包
        print("🔄 尝试使用7-zip解包...")
        try:
            cmd = f'7z x "{self.cpio_file}" -o"{self.extract_dir}" -y'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ 7-zip解包成功!")
                print(f"📁 文件已解包到: {self.extract_dir}")
                return True
            else:
                print(f"⚠️ 7-zip解包失败: {result.stderr}")
        except Exception as e:
            print(f"⚠️ 7-zip解包异常: {e}")
        
        # 尝试使用WinRAR解包
        print("🔄 尝试使用WinRAR解包...")
        try:
            cmd = f'winrar x "{self.cpio_file}" "{self.extract_dir}\\"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ WinRAR解包成功!")
                print(f"📁 文件已解包到: {self.extract_dir}")
                return True
            else:
                print(f"⚠️ WinRAR解包失败")
        except Exception as e:
            print(f"⚠️ WinRAR解包异常: {e}")

        # 尝试使用Python CPIO处理器
        print("🔄 尝试使用Python CPIO处理器...")
        try:
            success = self.cpio_handler.extract_cpio(self.cpio_file, self.extract_dir)
            if success:
                print("✓ Python CPIO处理器解包成功!")
                print(f"📁 文件已解包到: {self.extract_dir}")
                return True
            else:
                print("⚠️ Python CPIO处理器解包失败")
        except Exception as e:
            print(f"⚠️ Python CPIO处理器异常: {e}")

        print("❌ 所有解包方法都失败了")
        print("💡 请确保安装了7-zip或WinRAR，或检查CPIO文件格式")
        return False
        
    def create_cpio(self):
        """重新打包CPIO文件"""
        print("📦 开始重新打包CPIO文件...")
        
        if not self.extract_dir.exists():
            print("❌ 解包目录不存在")
            return False
            
        # 创建备份
        if self.cpio_file.exists():
            self.create_backup_dir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"ramdisk_{timestamp}.cpio"
            shutil.copy2(self.cpio_file, backup_file)
            print(f"✓ 创建原文件备份: {backup_file.name}")
        
        # 创建新的CPIO文件
        new_cpio = self.cpio_file.parent / "new_ramdisk.cpio"
        
        # 尝试使用7-zip打包
        print("🔄 尝试使用7-zip打包...")
        try:
            cmd = f'7z a -tcpio "{new_cpio}" *'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=self.extract_dir)
            if result.returncode == 0:
                print("✓ 7-zip打包成功!")
                
                # 计算新文件哈希
                new_hash = self.calculate_hash(new_cpio)
                if new_hash:
                    print(f"📋 新文件MD5: {new_hash}")
                
                # 询问是否替换原文件
                replace = input(f"是否用新文件替换原始的 {self.cpio_file.name}? (y/N): ")
                if replace.lower() == 'y':
                    shutil.move(new_cpio, self.cpio_file)
                    print(f"✓ 已替换原始文件: {self.cpio_file.name}")
                else:
                    print(f"✓ 新文件保存为: {new_cpio.name}")
                
                print("🎉 打包完成!")
                return True
            else:
                print(f"⚠️ 7-zip打包失败: {result.stderr}")
        except Exception as e:
            print(f"⚠️ 7-zip打包异常: {e}")

        # 尝试使用Python CPIO处理器
        print("🔄 尝试使用Python CPIO处理器...")
        try:
            success = self.cpio_handler.create_cpio(self.extract_dir, new_cpio)
            if success:
                print("✓ Python CPIO处理器打包成功!")

                # 计算新文件哈希
                new_hash = self.calculate_hash(new_cpio)
                if new_hash:
                    print(f"📋 新文件MD5: {new_hash}")

                # 询问是否替换原文件
                replace = input(f"是否用新文件替换原始的 {self.cpio_file.name}? (y/N): ")
                if replace.lower() == 'y':
                    shutil.move(new_cpio, self.cpio_file)
                    print(f"✓ 已替换原始文件: {self.cpio_file.name}")
                else:
                    print(f"✓ 新文件保存为: {new_cpio.name}")

                print("🎉 打包完成!")
                return True
            else:
                print("⚠️ Python CPIO处理器打包失败")
        except Exception as e:
            print(f"⚠️ Python CPIO处理器异常: {e}")

        print("❌ 打包失败")
        print("💡 请确保安装了7-zip，或检查源目录结构")
        return False
        
    def verify_integrity(self):
        """验证文件完整性"""
        print("🔍 验证文件完整性...")
        
        if not self.extract_dir.exists():
            print("❌ 解包目录不存在")
            return False
            
        original_path = self.work_dir / "recovery" / "ramdisk"
        if not original_path.exists():
            print("❌ 原始解包目录不存在")
            return False
        
        try:
            extract_files = list(self.extract_dir.rglob('*'))
            original_files = list(original_path.rglob('*'))
            
            print(f"📊 解包目录文件数: {len(extract_files)}")
            print(f"📊 原始目录文件数: {len(original_files)}")
            
            if len(extract_files) == len(original_files):
                print("✓ 文件数量匹配")
                return True
            else:
                print("⚠️ 文件数量不匹配")
                return False
                
        except Exception as e:
            print(f"⚠️ 验证过程中出现问题: {e}")
            return False
            
    def create_backup(self):
        """创建备份"""
        print("💾 创建备份...")
        
        if not self.cpio_file.exists():
            print("❌ CPIO文件不存在")
            return False
            
        self.create_backup_dir()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"ramdisk_{timestamp}.cpio"
        
        try:
            shutil.copy2(self.cpio_file, backup_file)
            print(f"✓ 备份创建成功: {backup_file.name}")
            return True
        except Exception as e:
            print(f"❌ 备份创建失败: {e}")
            return False
            
    def list_backups(self):
        """列出备份文件"""
        if not self.backup_dir.exists():
            print("❌ 备份目录不存在")
            return []
            
        backup_files = list(self.backup_dir.glob("*.cpio"))
        if not backup_files:
            print("❌ 没有找到备份文件")
            return []
            
        print("📋 可用的备份文件:")
        for i, backup_file in enumerate(backup_files, 1):
            stat = backup_file.stat()
            size_mb = stat.st_size / 1024 / 1024
            mtime = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            print(f"  [{i}] {backup_file.name} ({size_mb:.2f} MB, {mtime})")
            
        return backup_files
        
    def restore_backup(self):
        """恢复备份"""
        print("🔄 恢复备份...")
        
        backup_files = self.list_backups()
        if not backup_files:
            return False
            
        try:
            choice = input("请输入要恢复的备份文件编号: ")
            index = int(choice) - 1
            
            if 0 <= index < len(backup_files):
                backup_file = backup_files[index]
                shutil.copy2(backup_file, self.cpio_file)
                print(f"✓ 备份恢复成功: {backup_file.name}")
                return True
            else:
                print("❌ 无效的选择")
                return False
        except (ValueError, IndexError):
            print("❌ 无效的输入")
            return False
        except Exception as e:
            print(f"❌ 备份恢复失败: {e}")
            return False
            
    def show_menu(self):
        """显示菜单"""
        print("请选择操作:")
        print("[1] 解包 CPIO 文件")
        print("[2] 重新打包为 CPIO")
        print("[3] 验证文件完整性")
        print("[4] 创建备份")
        print("[5] 恢复备份")
        print("[6] 查看文件信息")
        print("[0] 退出")
        print()
        
    def run(self):
        """运行CLI"""
        self.print_header()
        
        while True:
            self.print_status()
            self.show_menu()
            
            try:
                choice = input("请输入选择 (0-6): ").strip()
                print()
                
                if choice == '1':
                    self.extract_cpio()
                elif choice == '2':
                    self.create_cpio()
                elif choice == '3':
                    self.verify_integrity()
                elif choice == '4':
                    self.create_backup()
                elif choice == '5':
                    self.restore_backup()
                elif choice == '6':
                    # 文件信息已在print_status中显示
                    pass
                elif choice == '0':
                    print("👋 感谢使用! By.举个🌰")
                    break
                else:
                    print("❌ 无效的选择，请重新输入")
                    
                print()
                input("按回车键继续...")
                print("\n" + "=" * 60 + "\n")
                
            except KeyboardInterrupt:
                print("\n\n👋 程序已退出")
                break
            except Exception as e:
                print(f"❌ 程序出错: {e}")
                input("按回车键继续...")

if __name__ == '__main__':
    app = RamdiskCLI()
    app.run()
