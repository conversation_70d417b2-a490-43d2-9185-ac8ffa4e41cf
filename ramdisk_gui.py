#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android Ramdisk CPIO 图形界面管理工具
Copyright 2025 By.举个🌰

使用tkinter创建的图形界面工具，用于管理ramdisk.cpio文件
"""

import os
import sys
import shutil
import subprocess
import hashlib
import threading
import time
from pathlib import Path
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext

class RamdiskGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Android Ramdisk CPIO 管理工具 - By.举个🌰")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 工作目录
        self.work_dir = Path.cwd()
        self.cpio_file = self.work_dir / "recovery" / "ramdisk.cpio"
        self.extract_dir = self.work_dir / "ramdisk_extracted"
        self.backup_dir = self.work_dir / "backup"
        
        self.setup_ui()
        self.update_status()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Android Ramdisk CPIO 管理工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 文件信息框架
        info_frame = ttk.LabelFrame(main_frame, text="文件信息", padding="10")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)
        
        # CPIO文件路径
        ttk.Label(info_frame, text="CPIO文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.cpio_path_var = tk.StringVar(value=str(self.cpio_file))
        cpio_entry = ttk.Entry(info_frame, textvariable=self.cpio_path_var, state='readonly')
        cpio_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(info_frame, text="浏览", command=self.browse_cpio_file).grid(row=0, column=2)
        
        # 解包目录
        ttk.Label(info_frame, text="解包目录:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.extract_path_var = tk.StringVar(value=str(self.extract_dir))
        extract_entry = ttk.Entry(info_frame, textvariable=self.extract_path_var, state='readonly')
        extract_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        
        ttk.Button(info_frame, text="浏览", command=self.browse_extract_dir).grid(row=1, column=2, pady=(5, 0))
        
        # 状态信息
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(info_frame, textvariable=self.status_var, foreground='blue')
        status_label.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 操作按钮框架
        button_frame = ttk.LabelFrame(main_frame, text="操作", padding="10")
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮网格布局
        buttons = [
            ("解包 CPIO", self.extract_cpio, 0, 0),
            ("重新打包", self.create_cpio, 0, 1),
            ("验证完整性", self.verify_integrity, 0, 2),
            ("创建备份", self.create_backup, 1, 0),
            ("恢复备份", self.restore_backup, 1, 1),
            ("查看文件信息", self.show_file_info, 1, 2),
        ]
        
        for text, command, row, col in buttons:
            btn = ttk.Button(button_frame, text=text, command=command, width=15)
            btn.grid(row=row, column=col, padx=5, pady=5)
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 版权信息
        copyright_label = ttk.Label(main_frame, text="Copyright 2025 By.举个🌰", 
                                   font=('Arial', 8), foreground='gray')
        copyright_label.grid(row=5, column=0, columnspan=2)
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
        
    def update_status(self):
        """更新状态信息"""
        cpio_exists = Path(self.cpio_path_var.get()).exists()
        extract_exists = Path(self.extract_path_var.get()).exists()
        
        if cpio_exists and extract_exists:
            self.status_var.set("✓ CPIO文件和解包目录都存在")
        elif cpio_exists:
            self.status_var.set("✓ CPIO文件存在，可以解包")
        elif extract_exists:
            self.status_var.set("✓ 解包目录存在，可以打包")
        else:
            self.status_var.set("❌ 请选择CPIO文件")
            
    def browse_cpio_file(self):
        """浏览选择CPIO文件"""
        filename = filedialog.askopenfilename(
            title="选择CPIO文件",
            filetypes=[("CPIO文件", "*.cpio"), ("所有文件", "*.*")]
        )
        if filename:
            self.cpio_path_var.set(filename)
            self.cpio_file = Path(filename)
            self.update_status()
            
    def browse_extract_dir(self):
        """浏览选择解包目录"""
        dirname = filedialog.askdirectory(title="选择解包目录")
        if dirname:
            self.extract_path_var.set(dirname)
            self.extract_dir = Path(dirname)
            self.update_status()
            
    def start_progress(self):
        """开始进度条动画"""
        self.progress.start(10)
        
    def stop_progress(self):
        """停止进度条动画"""
        self.progress.stop()
        
    def calculate_hash(self, file_path):
        """计算文件MD5哈希"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.log(f"❌ 计算哈希失败: {e}")
            return None
            
    def create_backup_dir(self):
        """创建备份目录"""
        if not self.backup_dir.exists():
            self.backup_dir.mkdir()
            self.log(f"✓ 创建备份目录: {self.backup_dir}")
            
    def extract_cpio(self):
        """解包CPIO文件"""
        def extract_thread():
            try:
                self.start_progress()
                cpio_path = Path(self.cpio_path_var.get())
                extract_path = Path(self.extract_path_var.get())
                
                if not cpio_path.exists():
                    self.log("❌ CPIO文件不存在")
                    return
                    
                self.log(f"📦 开始解包: {cpio_path.name}")
                
                # 创建备份
                self.create_backup_dir()
                backup_file = self.backup_dir / f"{cpio_path.name}.backup"
                if not backup_file.exists():
                    shutil.copy2(cpio_path, backup_file)
                    self.log(f"✓ 创建备份: {backup_file.name}")
                
                # 计算原始哈希
                original_hash = self.calculate_hash(cpio_path)
                if original_hash:
                    self.log(f"📋 原始文件MD5: {original_hash}")
                
                # 删除现有解包目录
                if extract_path.exists():
                    shutil.rmtree(extract_path)
                    self.log(f"🗑️ 删除现有目录: {extract_path}")
                
                extract_path.mkdir(parents=True)
                
                # 尝试使用7-zip解包
                success = False
                try:
                    cmd = f'7z x "{cpio_path}" -o"{extract_path}" -y'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=extract_path.parent)
                    if result.returncode == 0:
                        self.log("✓ 7-zip解包成功!")
                        success = True
                    else:
                        self.log(f"⚠️ 7-zip解包失败: {result.stderr}")
                except Exception as e:
                    self.log(f"⚠️ 7-zip解包异常: {e}")
                
                # 如果7-zip失败，尝试Python方式
                if not success:
                    try:
                        self.log("🔄 尝试使用Python解包...")
                        # 这里可以添加Python的CPIO解包代码
                        self.log("⚠️ Python解包功能待实现")
                    except Exception as e:
                        self.log(f"❌ Python解包失败: {e}")
                
                if success:
                    self.log(f"🎉 解包完成! 文件保存在: {extract_path}")
                    self.update_status()
                else:
                    self.log("❌ 所有解包方法都失败了")
                    
            except Exception as e:
                self.log(f"❌ 解包过程出错: {e}")
            finally:
                self.stop_progress()
                
        threading.Thread(target=extract_thread, daemon=True).start()
        
    def create_cpio(self):
        """重新打包CPIO文件"""
        def create_thread():
            try:
                self.start_progress()
                extract_path = Path(self.extract_path_var.get())
                cpio_path = Path(self.cpio_path_var.get())
                
                if not extract_path.exists():
                    self.log("❌ 解包目录不存在")
                    return
                    
                self.log(f"📦 开始打包: {extract_path}")
                
                # 创建备份
                if cpio_path.exists():
                    self.create_backup_dir()
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_file = self.backup_dir / f"ramdisk_{timestamp}.cpio"
                    shutil.copy2(cpio_path, backup_file)
                    self.log(f"✓ 创建原文件备份: {backup_file.name}")
                
                # 创建新的CPIO文件
                new_cpio = cpio_path.parent / "new_ramdisk.cpio"
                
                # 尝试使用7-zip打包
                success = False
                try:
                    cmd = f'7z a -tcpio "{new_cpio}" *'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=extract_path)
                    if result.returncode == 0:
                        self.log("✓ 7-zip打包成功!")
                        success = True
                    else:
                        self.log(f"⚠️ 7-zip打包失败: {result.stderr}")
                except Exception as e:
                    self.log(f"⚠️ 7-zip打包异常: {e}")
                
                if success:
                    # 计算新文件哈希
                    new_hash = self.calculate_hash(new_cpio)
                    if new_hash:
                        self.log(f"📋 新文件MD5: {new_hash}")
                    
                    # 询问是否替换原文件
                    replace = messagebox.askyesno("替换文件", f"是否用新文件替换原始的 {cpio_path.name}?")
                    if replace:
                        shutil.move(new_cpio, cpio_path)
                        self.log(f"✓ 已替换原始文件: {cpio_path.name}")
                    else:
                        self.log(f"✓ 新文件保存为: {new_cpio.name}")
                    
                    self.log("🎉 打包完成!")
                else:
                    self.log("❌ 打包失败")
                    
            except Exception as e:
                self.log(f"❌ 打包过程出错: {e}")
            finally:
                self.stop_progress()
                
        threading.Thread(target=create_thread, daemon=True).start()
        
    def verify_integrity(self):
        """验证文件完整性"""
        def verify_thread():
            try:
                self.start_progress()
                self.log("🔍 开始验证文件完整性...")
                
                extract_path = Path(self.extract_path_var.get())
                original_path = self.work_dir / "recovery" / "ramdisk"
                
                if not extract_path.exists():
                    self.log("❌ 解包目录不存在")
                    return
                    
                if not original_path.exists():
                    self.log("❌ 原始解包目录不存在")
                    return
                
                # 统计文件数量
                try:
                    extract_files = list(extract_path.rglob('*'))
                    original_files = list(original_path.rglob('*'))
                    
                    self.log(f"📊 解包目录文件数: {len(extract_files)}")
                    self.log(f"📊 原始目录文件数: {len(original_files)}")
                    
                    if len(extract_files) == len(original_files):
                        self.log("✓ 文件数量匹配")
                    else:
                        self.log("⚠️ 文件数量不匹配")
                        
                except Exception as e:
                    self.log(f"⚠️ 验证过程中出现问题: {e}")
                
                self.log("✓ 验证完成")
                
            except Exception as e:
                self.log(f"❌ 验证过程出错: {e}")
            finally:
                self.stop_progress()
                
        threading.Thread(target=verify_thread, daemon=True).start()
        
    def create_backup(self):
        """创建备份"""
        cpio_path = Path(self.cpio_path_var.get())
        if not cpio_path.exists():
            self.log("❌ CPIO文件不存在")
            return
            
        self.create_backup_dir()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"ramdisk_{timestamp}.cpio"
        
        try:
            shutil.copy2(cpio_path, backup_file)
            self.log(f"✓ 备份创建成功: {backup_file.name}")
        except Exception as e:
            self.log(f"❌ 备份创建失败: {e}")
            
    def restore_backup(self):
        """恢复备份"""
        if not self.backup_dir.exists():
            self.log("❌ 备份目录不存在")
            return
            
        backup_files = list(self.backup_dir.glob("*.cpio"))
        if not backup_files:
            self.log("❌ 没有找到备份文件")
            return
            
        # 选择备份文件
        backup_file = filedialog.askopenfilename(
            title="选择要恢复的备份文件",
            initialdir=self.backup_dir,
            filetypes=[("CPIO文件", "*.cpio"), ("所有文件", "*.*")]
        )
        
        if backup_file:
            cpio_path = Path(self.cpio_path_var.get())
            try:
                shutil.copy2(backup_file, cpio_path)
                self.log(f"✓ 备份恢复成功: {Path(backup_file).name}")
            except Exception as e:
                self.log(f"❌ 备份恢复失败: {e}")
                
    def show_file_info(self):
        """显示文件信息"""
        cpio_path = Path(self.cpio_path_var.get())
        extract_path = Path(self.extract_path_var.get())
        
        self.log("📋 文件信息:")
        
        if cpio_path.exists():
            stat = cpio_path.stat()
            size_mb = stat.st_size / 1024 / 1024
            self.log(f"  CPIO文件: {cpio_path.name}")
            self.log(f"  文件大小: {stat.st_size:,} 字节 ({size_mb:.2f} MB)")
            
            hash_value = self.calculate_hash(cpio_path)
            if hash_value:
                self.log(f"  MD5哈希: {hash_value}")
        else:
            self.log("  ❌ CPIO文件不存在")
            
        if extract_path.exists():
            try:
                files = list(extract_path.rglob('*'))
                dirs = [f for f in files if f.is_dir()]
                regular_files = [f for f in files if f.is_file()]
                
                self.log(f"  解包目录: {extract_path.name}")
                self.log(f"  总项目数: {len(files)}")
                self.log(f"  目录数: {len(dirs)}")
                self.log(f"  文件数: {len(regular_files)}")
            except Exception as e:
                self.log(f"  ⚠️ 统计解包目录时出错: {e}")
        else:
            self.log("  ❌ 解包目录不存在")
            
        # 显示备份信息
        if self.backup_dir.exists():
            backup_files = list(self.backup_dir.glob("*.cpio"))
            self.log(f"  备份文件数: {len(backup_files)}")
        else:
            self.log("  备份文件数: 0")
            
    def run(self):
        """运行GUI"""
        self.log("🚀 Android Ramdisk CPIO 管理工具启动")
        self.log("Copyright 2025 By.举个🌰")
        self.log("=" * 50)
        self.root.mainloop()

if __name__ == '__main__':
    app = RamdiskGUI()
    app.run()
