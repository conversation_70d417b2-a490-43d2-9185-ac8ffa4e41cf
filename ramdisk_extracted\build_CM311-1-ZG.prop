#
# ADDITIONAL_DEFAULT_PROPERTIES
#
ro.actionable_compatible_property.enabled=true
ro.secure=1
security.perf_harden=1
ro.allow.mock.location=0
ro.debuggable=1
tombstoned.max_tombstone_count=50
dalvik.vm.image-dex2oat-Xms=64m
dalvik.vm.image-dex2oat-Xmx=64m
dalvik.vm.dex2oat-Xms=64m
dalvik.vm.dex2oat-Xmx=512m
dalvik.vm.usejit=true
dalvik.vm.usejitprofiles=true
dalvik.vm.dexopt.secondary=true
dalvik.vm.appimageformat=lz4
pm.dexopt.first-boot=quicken
pm.dexopt.boot=verify
pm.dexopt.install=speed-profile
pm.dexopt.bg-dexopt=speed-profile
pm.dexopt.ab-ota=speed-profile
pm.dexopt.inactive=verify
pm.dexopt.shared=speed
dalvik.vm.dex2oat-minidebuginfo=true
debug.atrace.tags.enableflags=0
persist.bluetooth.btsnoopenable=false
persist.bluetooth.btsnooppath=/data/misc/bluedroid/btsnoop_hci.cfa
persist.bluetooth.btsnoopsize=0xffff
persist.bluetooth.showdeviceswithoutnames=false
vendor.bluetooth.enable_timeout_ms=11000
#
# BOOTIMAGE_BUILD_PROPERTIES
#
ro.bootimage.build.date=Fri Jun 7 08:34:45 CST 2024
ro.bootimage.build.date.utc=1717720485
ro.bootimage.build.fingerprint=p291_iptv/p291_iptv/p291_iptv:9/PPR1.180610.011/20240607:userdebug/test-keys
persist.sys.usb.config=none
#
# ADDITIONAL VENDOR DEFAULT PROPERTIES
#
ro.vndk.version=28
ro.zygote=zygote32
ro.logd.size.stats=64K
log.tag.stats_log=I

# begin build properties
# autogenerated by buildinfo.sh
ro.build.id=CM311-1-ZG
ro.build.display.id=CM311-1-ZG test-keys
ro.build.version.incremental=V.686.03
ro.build.version.sdk=28
ro.build.version.preview_sdk=0
ro.build.version.codename=REL
ro.build.version.all_codenames=REL
ro.build.version.release=9
ro.build.version.security_patch=2018-08-05
ro.build.version.base_os=
ro.build.version.min_supported_target_sdk=17
ro.build.date=Thu Jun 13 19:56:27 CST 2024
ro.build.date.utc=1718279787
ro.build.type=userdebug
ro.build.user=jenkins
ro.build.host=cmcc
ro.build.tags=test-keys
ro.build.flavor=p291_iptv-userdebug
ro.build.system_root_image=false
ro.product.model=CM311-1-ZG
ro.product.brand=CM311-1-ZG
ro.product.name=CM311-1-ZG
ro.product.device=p291_iptv
# ro.product.cpu.abi and ro.product.cpu.abi2 are obsolete,
# use ro.product.cpu.abilist instead.
ro.product.cpu.abi=armeabi-v7a
ro.product.cpu.abi2=armeabi
ro.product.cpu.abilist=armeabi-v7a,armeabi
ro.product.cpu.abilist32=armeabi-v7a,armeabi
ro.product.cpu.abilist64=
ro.product.manufacturer=CMDC
ro.product.manufactureroui=FF0001
ro.product.locale=zh-CN


ro.wifi.channels=
# ro.build.product is obsolete; use ro.product.device
ro.build.product=CM311-1-ZG
# Do not try to parse description, fingerprint, or thumbprint
ro.build.description=Amlogic905L3-eng 9.0.0 CM311-1-ZG V.686.03 test-keys
ro.build.fingerprint=AndroidP/CMDC/S905L3:9.0.0/CM311-1-ZG/V.686.03:eng/test-keys
ro.build.characteristics=device,mbx,nosdcard
ro.build.aml.baseline="AmlogicIPTVAndroidP_R210531_T20220709"
# end build properties
#
# from device/amlogic/p291_iptv/system_chinamobile.prop
#
#ro.product.firmware=00442001
#ro.product.otaupdateurl=http://***********:8080/otaupdate/update
#ro.adb.secure=1
#ro.vlan.enable=false
#rild.libpath=/system/lib/libreference-ril.so
#rild.libargs=-d /dev/ttyS0
#ro.sf.lcd_density=240
#keyguard.no_require_sim=1

#persist.sys.autosuspend.cec.enable=true
#persist.sys.mbx.timeout.enable=false
#persist.sys.autosuspend.cec.time=1000
#sys.screensaver.enable=false
#ro.statusbar.widget=false
#ro.statusbar.button=false
#ro.statusbar.yearmonthdayweek=false

#wifi.interface=ra0
# Time between scans in seconds. Keep it high to minimize battery drain.
# This only affects the case in which there are remembered access points,
# but none are in range.
#wifi.supplicant_scan_interval = 60
#alsa.mixer.playback.master=DAC2 Analog
#alsa.mixer.capture.master=Analog

#hwui.render_dirty_regions=false

#force GPU rendering
#persist.sys.ui.hw=true

#timeout control
http.getc.timeout_us = 10000000

# Configure features
#hw.nopm=true
#hw.nobattery=true
#hw.nophone=true
#hw.novibrate=true
#hw.nogps=true
#hw.cameras=0
#hw.hasethernet=true
#hw.hasusbcamera=true
#hw.has.accelerometer=true

#used in packages/apps/Settings/.../Utils.java
#hw.has.bluetooth=false
#ro.ethernet.default_on=true
#ro.config.low_ram=false
#dalvik.vm.jit.codecachesize=0

#ro.screen.has.timeout=false
#ro.screen.has.brightness=false
#ro.screen.has.tvout=true
#ro.platform.has.security=false
#ro.platform.has.tts=false
#ro.platform.has.touch=false
#ro.platform.has.mbxuimode=true
#ro.platform.has.1080scale=2
#ro.fontScale=1.3
#ro.platform.has.digitaudio=true
#ro.platform.has.defaulttvfreq=false
#ro.hw.ethernet.onboard=true
#ro.platform.has.cvbsmode=false
#ro.platform.hdmionly=true
#ro.platform.has.cecmode=false
#ro.platform.filter.modes=480i60hz,576i50hz,smpte24hz
#ro.platform.filter.modes=2160p50hz42010bit,2160p60hz42010bit,2160p50hz42212bit,2160p60hz42212bit
#sys.output.10bit=true

#ro.platform.has.systemlog=false

# Use OSD2 mouse patch
#ro.ui.cursor=osd2
#ro.ui.cursor.autohide=true
#ro.ui.cursor.timeout=10000

#ro.ui.cursor=surface

#set to 0 temporarily so touch works without other changes
#ro.sf.hwrotation=0

#ro.hardware=amlogic

#camera 1080p
#ro.camera.preview.MaxSize=1920x1088
#ro.camera.preview.LimitedRate=1920x1088x30,1280x720x30,640x480x30,320x240x28
#ro.camera.preview.UseMJPEG=1
#ro.camera.preview.UseHwMJPEG=1
#enable mjpeg multi-dec
media.omx.multi_mode=11

#hw.encoder.freerun=1
#hw.encoder.temp.enable=1
#hw.encoder.reencode.enable=0

# Enable 32-bit OSD
#sys.fb.bits=32

# Disable GPS
#gps.enable=false

#keep landscape in launcher
#sys.keeplauncher.landcape=true

#Enable player buildin
media.amsuperplayer.enable=true
media.amplayer.enable-acodecs=ac3,eac3,rm,dts,thd,aac,m4a,asf
media.amplayer.enable=true
media.amplayer.support-exname=divx,h264,avi,ts,m2ts,mkv,mp4,mpg,mpeg,rm,rmvb,wmv,ts,dat,vob,vc1
media.amplayer.support-exname1=mp2,mp3,ac3
media.amsuperplayer.m4aplayer=STAGEFRIGHT_PLAYER
media.amsuperplayer.defplayer=PV_PLAYER
media.amplayer.thumbnail=true
media.amplayer.thumbnail4http=true
media.amplayer.startmode=true
media.playstatus.enable=1

#FOR ffmpeg cache module
libplayer.cache.debug=1
libplayer.cache.enable=1
libplayer.cache.seekenable=1
libplayer.cache.keepframe_en=0
libplayer.cache.enterkeepms=5000
libplayer.cache.amaxframes=4000000
libplayer.cache.vmaxframes=2000000
libplayer.cache.smaxframes=2000000
libplayer.cache.maxmem=94371840
libplayer.cache.backseek=300
libplayer.cache.bigpktnum=4
media.player.seekX=0
media.libplayer.nobufferstart=0
media.amplayer.seek_async=0
media.amplayer.chmobilekpi=0
libplayer.tcp.get_dns_type=0

sys.imgplayer.freq_down=1
#FOR IPTV
libplayer.ffmpeg.lpbufsizemax=10485760
libplayer.switch.format = 1
media.amplayer.lpbufferlevel=0.002
media.amplayer.onbuffering.S=0.3
media.amplayer.buffertime=6
media.amplayer.lowlevel=0.001
media.amplayer.midlevel=0.07
media.amplayer.middlelevel.4k=0.1
media.amplayer.highlevel=0.95
media.amplayer.divtime=1
media.amplayer.force_buf_enable=1
media.amplayer.force_buf_thres=400
media.amadec.prefilltime=50
media.amplayer.force_buf_exit=0.008
media.amplayer.refmode=0
media.amplayer.dropmaxtime=10000
media.amplayer.pre_droppcm=0
#FOR IPTV,delay send buffering.
media.amplayer.delaybuffering.s=2
media.amplayer.delayprobebuffering.s=0
media.amplayer.delaybuffering=3
media.amplayer.smooth_region=7200000000
media.amplayer.bufing_timeout=90

#update state interval
#media.amplayer.update_interval=500
# deep diagnose
media.amplayer.hls_notify_en=1
media.hls.range_type=1
libplayer.tcp.timeout=1000
libplayer.tcp.dnscache=1
libplayer.hls.start_from_top=1

#update duration with url info
media.amplayer.dur_update=1
media.amplayer.lpbufferlevel=0.05
#media.p2pplay.enable=true
media.amplayer.widevineenable=false
media.amplayer.dsource4local=1
media.arm.audio.decoder=ape,flac,dts,ac3,eac3,wma,wmapro,mp3,aac,vorbis,raac,cook,amr,pcm,adpcm,aac_latm,rm
media.wfd.use-pcm-audio=true
media.wfd.videoreolutiontype=0
media.wfd.videoreolutiongroup=5
media.wfd.videoframerate=20
media.html5videowin.enable=1
media.decoder.vfm.defmap=decoder ppmgr deinterlace amvideo
media.amplayer.seekkeyframe=1
media.amplayer.dropwaitxms=2000
media.amplayer.seekmode=1
media.omx.LowLatency_mode=1
mbx.3D_Bright.enable=false
# Nand write need force sync when gadget
#gadget.nand.force_sync=true
#enable bluetooth when suspend
#bt.keep_on.enable=true
#bt.auto.enable=true

# Camera exif
#ro.camera.exif.make=M8b
#ro.camera.exif.model=m200
#hide status bar
#persist.sys.hideStatusBar=true

#support media poll uevent,can use sd cardread on usb port
#has.media.poll=true

#used forward seek for libplayer
media.libplayer.seek.fwdsearch=1
media.libplayer.net.frameseek=1


# Disable preload-class
#ro.amlogic.no.preloadclass=1

#virtualsd.enable=true

#service.adb.tcp.port=5555
#const.window.w=1280
#const.window.h=720
#sys.defaultStream.ismusic=true
#ro.app.optimization=true
#ro.platform.has.realoutputmode=true

#add livhls,libcurl as default hls
media.libplayer.curlenable=false
media.libplayer.modules=vhls_mod,dash_mod,curl_mod,prhls_mod,vm_mod,bluray_mod

#sofrware demux
libplayer.netts.recalcpts=1
libplayer.livets.softdemux=1
libplayer.ts.softdemux=1
libplayer.hls.stpos=0
#Time Zone
persist.sys.timezone=Asia/Shanghai
#ro.osd2.size=64x64

#new feature 81282
#ro.alarm.align=true

#for AmUMediaPlayer hls
#media.ammediaplayer.enable=1
hls.aml.enable=1
hls.curl.enable=0

#for AmUMediaPlayer Circle Buffer
iptv.enablebuf=0

#add for video boot, 1 means use video boot, others not .
#service.bootvideo=0
#service.bootadv=1

#service.bootvideo.checkexit=false
#ro.product.bootvideo.type=zip

#new feature 81297
#ro.permissions.settings=true

#don't need brightness
#prop.sp.brightness=off
#ro.quickboot.enable=false

#config.disable_telephony=true
#config.disable_bluetooth=true
#config.enable_quickboot=true
#config.disable_vibrator=true
#config.disable_location=true

#has spdif output
#ro.hdmi.spdif=true

#for China mobile
net.ppp.retrycount=2
persist.sys.autosuspend.hdmi=false
sys.deepdiagnose.support=1
sys.wifi.ipv6.enable=true
sys.broadcast.permit=true
persist.net.monitor=true

#for all cec key on or off
persist.sys.cec.enable.key=true

#for cec volume key on or off
persist.sys.cec.enable.volume.key=false

#for cec control enable or disable
persist.vendor.sys.cec.controlenabled=false

ro.iptv.mbox=true
ro.autocreate.download=true
ro.product.name=CM311-1-ZG
net.pppoe.running=1
net.ethwifi.coexist=true
ro.mac=
epg.login=
epg.userid=
epg.token=
epg.mobile.userid=
epg.mobile.token=
epg.mobile.deviceid=
ro.media.timeshift=0
sys.settings.support=1
sys.settings.support.net.flags=7
epg.eccode=
epg.copyrightid=
epg.indexurl=
epg.cmcchomeurl=
epg.authcode=
epg.eccoporationcode=
epg.speechchannel.bussy=
epg.usergroup=
epg.accountidentity=
#ro.product.type=0
#ro.devicetype=stb
net.dhcpc.ipver=
net.dhcpc.username=
net.dhcpc.pswd=
net.dhcpc.option=
sys.cmcc.hls.adaptrate=
sys.cmcc.hls.firstrate=
sys.cmcc.video.contentmode=
service.media.playstatus=stopped
#sys.settings.support.languages=zh-CN,zh-HK,en-US
persist.sys.language=ZH EN
persist.sys.country=CN,HK,US
sys.app.oom_adj=1
#sys.deflauncher.cls=com.chinamobile.startup.activity.StartUpMainActivity
sys.deflaunchersetting.pkg=com.shcmcc.setting
sys.settings.support.bluetooth=1
#ro.flash.size=NAND 4G
#ro.memory.size=

#for webkit retry
webkit.loadurl.retry_cnt=3
webkit.loadurl.timeout=10

sys.settings.support.ap.flags=0

sys.settings.support.spdif=0
ro.media.dolby=0
ro.media.maxresolution=0
sys.settings.support.net.flags=7
sys.deepdiagnose.support=1

#K8 popo issue
persist.service.ki.builtin=0

#for webkit retry
webkit.loadurl.retry_cnt=3
webkit.loadurl.timeout=10
#ethernet & wifi coexist
net.ethwifi.prior=ethernet

sys.proj.type=mobile


#instaboot bootanimation config
#onetime: bootanimation run onetime
#loop: bootanimation run in loop
#none: no bootanimation when instaboot restored
#ro.instaboot.bootanimation=onetime
#ro.instaboot.animation_time=5
#net.pppoe.padt_timeout=600

#for tr069
#ro.tr069.enable=true
#sys.supend.delaytime=5000
#sys.start.boot=true
#sys.broadcast.policy=sticky
#media.player.cmcc_report.enable=true
#sys.support.smpte=true

#force vd1 for 3/4 player instances
vendor.hwc.forceOneLayer=1
#for kernel to logcat
#ro.logd.kernel=1
#ro.logd.size=2m
#for amlchat app
amlchat.status.enable=disable
#for default volume
persist.audio.volume=8
persist.sys.boot.volume=8
sys.shcmcc.test.hideview=true

#enable ffmpeg ipv6 tcp
media.libplayer.ipv4only=0

#enable liveplayer mediahal videotunnel
vendor.media.liveplayer.enable-mediahal-videodec=1
vendor.media.liveplayer.enable-video-tunnel=1
#suppoort mpeg2 audio in liveplayer
vendor.media.liveplayer.mpeg2-use-ffmpeg-audio-decoder=1
#set probe size on AmIptvMedia
iptv.probesize=8388608
#support report blur softprobe event
media.player.cmcc_report.enable=1

persist.sys.mscanner=false

#
# ADDITIONAL_BUILD_PROPERTIES
#
ro.product.first_api_level=28
ro.bionic.ld.warning=1
ro.art.hiddenapi.warning=1
ro.treble.enabled=true
persist.sys.dalvik.vm.lib.2=libart.so
dalvik.vm.isa.arm.variant=cortex-a9
dalvik.vm.isa.arm.features=default
dalvik.vm.lockprof.threshold=500
net.bt.name=Android
dalvik.vm.stack-trace-dir=/data/anr
ro.build.expect.bootloader=01.01.180822.145544

ro.product.first_api_level=28
ro.vendor.build.date=Fri Jun 7 08:34:45 CST 2024
ro.vendor.build.date.utc=1717720485
ro.vendor.build.fingerprint=p291_iptv/p291_iptv/p291_iptv:9/PPR1.180610.011/20240607:userdebug/test-keys
ro.vendor.build.security_patch=
ro.vendor.product.cpu.abilist=armeabi-v7a,armeabi
ro.vendor.product.cpu.abilist32=armeabi-v7a,armeabi
ro.vendor.product.cpu.abilist64=
# begin build properties
# autogenerated by vendor_buildinfo.sh
ro.product.board=p291_iptv
ro.board.platform=p291_iptv
ro.product.vendor.manufacturer=Droidlogic
ro.product.vendor.model=p291_iptv
ro.product.vendor.brand=Droidlogic
ro.product.vendor.name=p291_iptv
ro.product.vendor.device=p291_iptv
# end build properties
#
# ADDITIONAL VENDOR BUILD PROPERTIES
#
ro.vendor.platform.has.mbxuimode=true
ro.vendor.platform.has.realoutputmode=true
ro.vendor.platform.need.display.hdmicec=true
ro.media.camera_preview.maxsize=1920x1080
ro.media.camera_preview.limitedrate=1920x1080x30,1280x720x30,640x480x30,320x240x28
ro.media.camera_preview.usemjpeg=1
ro.vendor.platform.usehwmjpeg=true
ro.vendor.platform.omx=true
ro.vendor.autoconnectbt.isneed=false
ro.vendor.autoconnectbt.macprefix=00:CD:FF
ro.vendor.autoconnectbt.btclass=50c
ro.vendor.autoconnectbt.nameprefix=Amlogic_RC
ro.vendor.autoconnectbt.rssilimit=70
persist.vendor.bt_vendor=libbt-vendor_rtl.so
ro.radio.noril=false
ro.net.pppoe=true
ro.vendor.platform.support.dolbyvision=true
ro.vendor.platform.disable.audiorawout=false
ro.vendor.platform.support.dolby=true
ro.vendor.platform.support.dts=true
media.support.dolbyvision=true
service.bootvideo=0
drm.service.enabled=1
ro.media.maxmem=629145600
ro.audio.mapvalue=0,0,0,0
service.adb.tcp.port=5555
vendor.afbcd.enable=1
ro.config.low_ram=true
ro.vendor.low_power_default_color=true
ro.crypto.volume.filenames_mode=aes-256-cts
ro.vendor.sdr2hdr.enable=true
ro.vendor.platform.is.tv=0
persist.vendor.media.bootvideo=0050
ro.vendor.platform.hdmi.device_type=4
ro.vendor.platform.support.network_led=true
ro.config.media_vol_default=8
media.omx.display_mode=3
media.sf.omxvideo-optmize=1
media.amplayer.truehd=0
ro.product.name=CM311-1-ZG
ro.product.type=0
ro.product.manufactureroui=FF0001
ro.devicetype=stb
ro.media.timeshift=0
sys.settings.support.net.flags=7
sys.settings.support.ap.flags=0
sys.settings.support.languages=zh-CN
sys.deepdiagnose.support=1
service.media.playstatus=stopped
persist.sys.timezone=Asia/Shanghai
persist.sys.language=ZH,EN
persist.sys.country=CN,HK,US
sys.network.priority=6
libplayer.tcp.get_ip_priority=0
persist.sys.reverb_mode=0,4,5
ro.audio.usb.period_us=10000
media.audiohal.reverb_gain=75
persist.sys.mic_gain_value=-14.3,-13,-11.9,-10.9,-9,-7.4,-6.1,-4.9,-3.9,-3,-2.1,-1.5,-0.7,-0.1,0


media.amplayer.disable-vcodecs=real,divx,divx3,divx4
media.amplayer.skipvideotype=rv40,wmv1,wmv2,wmv3,rv30
media.amplayer.videolimiter=1
sys.sunniwell.v_unsupport=false
ro.media.softprobe.enable=true
sys.boot.kpi=false
ro.config.ringtone=Ring_Synth_04.ogg
ro.config.notification_sound=pixiedust.ogg
ro.carrier=unknown
debug.sf.disable_backpressure=1
debug.sf.latch_unsignaled=1
net.tethering.noprovisioning=true
ro.config.alarm_alert=Alarm_Classic.ogg
ro.dalvik.vm.native.bridge=0
camera.disable_zsl_mode=1
ro.media.camera_usb.faceback=false
ro.vendor.app.optimization=true
ro.sf.disable_triple_buffer=1
ro.vendor.vndk.version=26.1.0
dalvik.vm.heapgrowthlimit=256m
dalvik.vm.heapstartsize=50m
ro.boot.fake_battery=42
ro.af.client_heap_size_kbyte=1536
ro.hdmi.device_type=4
dalvik.vm.heapsize=384m
dalvik.vm.heaptargetutilization=0.75
dalvik.vm.heapminfree=512k
dalvik.vm.heapmaxfree=8m
ro.hdmi.set_menu_language=false
persist.sys.hdmi.keep_awake=false
persist.vendor.wifi_remove=true
wifi.interface=wlan0
wifi.direct.interface=p2p0
config.disable_bluetooth=false
ro.vendor.btmodule=multibt
wc_transport.soc_initialized=0
persist.vendor.bluetooth.rtkcoex=true
persist.vendor.rtkbt.bdaddr_path=none
persist.vendor.bluetooth.prefferedrole=master
persist.vendor.rtkbtadvdisable=false
poweroff.doubleclick=1
qcom.bluetooth.soc=rome_uart
ro.migukaraok=true
persist.sys.app.rotation=middle_port
persist.sys.isshowime=1
ro.opengles.version=131072
debug.hwui.use_buffer_age=false
media.cmccplayer.enable=0
media.amplayer.displast_frame=1
sys.amplayer.drop_pcm=1
media.amlogicplayer.enable=1
media.arm.audio.decoder=ape,flac,dts,ac3,eac3,wma,wmapro,mp3,aac,vorbis,raac,cook,amr,pcm,adpcm,aac_latm,rm
media.libplayer.modules=vhls_mod,dash_mod,curl_mod,prhls_mod,vm_mod,bluray_mod

ro.product.build.date=Fri Jun 7 08:34:45 CST 2024
ro.product.build.date.utc=1717720485
ro.product.build.fingerprint=p291_iptv/p291_iptv/p291_iptv:9/PPR1.180610.011/20240607:userdebug/test-keys
#
# ADDITIONAL PRODUCT PROPERTIES
#
ro.build.devicemodel=CM311-1-ZG
ro.hardwareno=CM311-1-ZG
ro.build.equipment=CM311-1-ZG
ro.factory.name=CMDC
persist.sys.wifi.dualstack=10
persist.sys.app.silentInstaller=true
ro.standby.action=net.sunniwell.action.POWER_WINDOW_SERVICE
sys.standby.mode=1
media.filter.heaac=1
ro.prop.heaac.filter=false
sys.platform.media=mlogic
sys.pop.ups=true
