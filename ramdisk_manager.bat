@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Android Ramdisk CPIO 管理工具
:: Copyright 2025 By.举个🌰

echo ============================================================
echo Android Ramdisk CPIO 管理工具
echo Copyright 2025 By.举个🌰
echo ============================================================
echo.

set "CPIO_FILE=recovery\ramdisk.cpio"
set "EXTRACT_DIR=ramdisk_extracted"
set "BACKUP_DIR=backup"
set "NEW_CPIO=new_ramdisk.cpio"

:: 检查原始文件是否存在
if not exist "%CPIO_FILE%" (
    echo ❌ 错误: 找不到 %CPIO_FILE%
    pause
    exit /b 1
)

:: 创建备份目录
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

:MENU
cls
echo ============================================================
echo Android Ramdisk CPIO 管理工具
echo Copyright 2025 By.举个🌰
echo ============================================================
echo.
echo 当前CPIO文件: %CPIO_FILE%
echo 解包目录: %EXTRACT_DIR%
echo.
echo 请选择操作:
echo [1] 解包 CPIO 文件
echo [2] 重新打包为 CPIO
echo [3] 验证文件完整性
echo [4] 创建备份
echo [5] 恢复备份
echo [6] 查看文件信息
echo [0] 退出
echo.
set /p choice="请输入选择 (0-6): "

if "%choice%"=="1" goto EXTRACT
if "%choice%"=="2" goto CREATE
if "%choice%"=="3" goto VERIFY
if "%choice%"=="4" goto BACKUP
if "%choice%"=="5" goto RESTORE
if "%choice%"=="6" goto INFO
if "%choice%"=="0" goto EXIT
goto MENU

:EXTRACT
echo.
echo 📦 开始解包 CPIO 文件...
echo.

:: 检查是否已存在解包目录
if exist "%EXTRACT_DIR%" (
    echo ⚠️  目标目录 %EXTRACT_DIR% 已存在
    set /p overwrite="是否删除现有目录并重新解包? (y/N): "
    if /i not "!overwrite!"=="y" (
        echo 操作已取消
        pause
        goto MENU
    )
    rmdir /s /q "%EXTRACT_DIR%"
)

mkdir "%EXTRACT_DIR%"

:: 尝试使用不同的方法解包
echo 🔄 尝试使用 7-zip 解包...
7z x "%CPIO_FILE%" -o"%EXTRACT_DIR%" -y >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ 7-zip 解包成功!
    goto EXTRACT_SUCCESS
)

echo 🔄 尝试使用 WinRAR 解包...
winrar x "%CPIO_FILE%" "%EXTRACT_DIR%\" >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ WinRAR 解包成功!
    goto EXTRACT_SUCCESS
)

echo 🔄 尝试使用 Python 脚本解包...
python ramdisk_tools.py extract -i "%CPIO_FILE%" -o "%EXTRACT_DIR%"
if !errorlevel! equ 0 (
    echo ✓ Python 脚本解包成功!
    goto EXTRACT_SUCCESS
)

echo ❌ 所有解包方法都失败了
echo 请确保安装了 7-zip、WinRAR 或 Python
pause
goto MENU

:EXTRACT_SUCCESS
echo.
echo 🎉 解包完成!
echo 📁 文件已解包到: %EXTRACT_DIR%
echo.
dir "%EXTRACT_DIR%" /b
echo.
pause
goto MENU

:CREATE
echo.
echo 📦 开始重新打包 CPIO 文件...
echo.

if not exist "%EXTRACT_DIR%" (
    echo ❌ 错误: 解包目录 %EXTRACT_DIR% 不存在
    echo 请先解包原始文件
    pause
    goto MENU
)

:: 创建原始文件备份
if exist "%CPIO_FILE%" (
    set "timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    set "timestamp=!timestamp: =0!"
    copy "%CPIO_FILE%" "%BACKUP_DIR%\ramdisk_!timestamp!.cpio" >nul
    echo ✓ 已创建原始文件备份
)

:: 尝试使用不同的方法打包
echo 🔄 尝试使用 7-zip 打包...
cd "%EXTRACT_DIR%"
7z a -tcpio "..\%NEW_CPIO%" * >nul 2>&1
cd ..
if !errorlevel! equ 0 (
    echo ✓ 7-zip 打包成功!
    goto CREATE_SUCCESS
)

echo 🔄 尝试使用 Python 脚本打包...
python ramdisk_tools.py create -i "%EXTRACT_DIR%" -o "%NEW_CPIO%"
if !errorlevel! equ 0 (
    echo ✓ Python 脚本打包成功!
    goto CREATE_SUCCESS
)

echo ❌ 打包失败
pause
goto MENU

:CREATE_SUCCESS
echo.
echo 🎉 打包完成!
echo 📄 新文件: %NEW_CPIO%
echo.
echo 是否替换原始文件?
set /p replace="替换 %CPIO_FILE% ? (y/N): "
if /i "!replace!"=="y" (
    move "%NEW_CPIO%" "%CPIO_FILE%"
    echo ✓ 已替换原始文件
) else (
    echo ✓ 新文件保存为: %NEW_CPIO%
)
echo.
pause
goto MENU

:VERIFY
echo.
echo 🔍 验证文件完整性...
echo.

if not exist "%EXTRACT_DIR%" (
    echo ❌ 错误: 解包目录不存在
    pause
    goto MENU
)

if not exist "recovery\ramdisk" (
    echo ❌ 错误: 原始解包目录不存在
    pause
    goto MENU
)

echo 📊 比较文件结构...
echo.
echo 原始目录 (recovery\ramdisk):
dir "recovery\ramdisk" /b /s | find /c /v "" 
echo.
echo 解包目录 (%EXTRACT_DIR%):
dir "%EXTRACT_DIR%" /b /s | find /c /v ""
echo.
echo ✓ 验证完成
pause
goto MENU

:BACKUP
echo.
echo 💾 创建备份...
echo.

set "timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "timestamp=!timestamp: =0!"
set "backup_file=%BACKUP_DIR%\ramdisk_!timestamp!.cpio"

copy "%CPIO_FILE%" "!backup_file!" >nul
if !errorlevel! equ 0 (
    echo ✓ 备份创建成功: !backup_file!
) else (
    echo ❌ 备份创建失败
)
echo.
pause
goto MENU

:RESTORE
echo.
echo 🔄 恢复备份...
echo.

if not exist "%BACKUP_DIR%\*.cpio" (
    echo ❌ 没有找到备份文件
    pause
    goto MENU
)

echo 可用的备份文件:
dir "%BACKUP_DIR%\*.cpio" /b
echo.
set /p backup_name="请输入要恢复的备份文件名: "

if exist "%BACKUP_DIR%\!backup_name!" (
    copy "%BACKUP_DIR%\!backup_name!" "%CPIO_FILE%" >nul
    echo ✓ 备份恢复成功
) else (
    echo ❌ 备份文件不存在
)
echo.
pause
goto MENU

:INFO
echo.
echo 📋 文件信息...
echo.

if exist "%CPIO_FILE%" (
    echo CPIO文件: %CPIO_FILE%
    dir "%CPIO_FILE%" | findstr /v "个文件"
    echo.
)

if exist "%EXTRACT_DIR%" (
    echo 解包目录: %EXTRACT_DIR%
    dir "%EXTRACT_DIR%" /b | find /c /v ""
    echo 个文件/目录
    echo.
)

echo 备份文件:
if exist "%BACKUP_DIR%\*.cpio" (
    dir "%BACKUP_DIR%\*.cpio" /b
) else (
    echo 无备份文件
)
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用! By.举个🌰
echo.
pause
exit /b 0
