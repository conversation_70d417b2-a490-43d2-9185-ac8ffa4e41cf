#*********************************************************************************************************
#this file is used to store key map table
# custom_name:	  name of map table
# custom_code:	  custom code for remote device
# release_delay	  unit:ms.release will report from kernel to user layer after this period of time
#			  from press or repeat triggered.
###PARAS FOR MOUSE MODE:
# fn_key_scancode: scancode of fn key which used to swith mode
# cursor_left_scancode: scancode of left key
# cursor_right_scancode:  scancode of right key
# cursor_up_scancode: scancode of up key
# cursor_down_scancode: scancode of down key
# cursor_ok_scancode: scancode of ok key
#************************************************************************************************************* 
custom_name = amlogic-remote-5
custom_code = 0xbd02
release_delay = 80

key_begin
		0xca 103
		0xd2 108
		0x99 105
		0xc1 106
		0xce 97
		0x45 179
		0xc5 133
		0x80 113
		0xd0 15
		0xd6 125
		0x95 102
		0xdd 104
		0x8c 109
		0x89 131
		0x9c 130
		0x9a 120
		0xcd 121
		0xf0 500
key_end
