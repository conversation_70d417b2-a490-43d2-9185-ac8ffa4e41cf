#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android Ramdisk CPIO 解包/打包工具
Copyright 2025 By.举个🌰

这个工具用于安全地解包和重新打包Android Recovery的ramdisk.cpio文件
支持保留原始文件权限、所有者信息和符号链接
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path
import tempfile
import hashlib

class RamdiskTool:
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.backup_dir = self.script_dir / "backup"
        self.work_dir = self.script_dir / "work"
        
    def create_backup(self, file_path):
        """创建原始文件的备份"""
        if not self.backup_dir.exists():
            self.backup_dir.mkdir()
            
        backup_file = self.backup_dir / f"{file_path.name}.backup"
        if not backup_file.exists():
            shutil.copy2(file_path, backup_file)
            print(f"✓ 已创建备份: {backup_file}")
        else:
            print(f"✓ 备份已存在: {backup_file}")
            
    def calculate_hash(self, file_path):
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
        
    def extract_cpio(self, cpio_file, extract_dir):
        """解包CPIO文件"""
        cpio_path = Path(cpio_file)
        extract_path = Path(extract_dir)
        
        if not cpio_path.exists():
            print(f"❌ 错误: CPIO文件不存在: {cpio_path}")
            return False
            
        # 创建备份
        self.create_backup(cpio_path)
        
        # 计算原始文件哈希
        original_hash = self.calculate_hash(cpio_path)
        print(f"📋 原始文件MD5: {original_hash}")
        
        # 创建解包目录
        if extract_path.exists():
            print(f"⚠️  目标目录已存在: {extract_path}")
            response = input("是否删除现有目录并重新解包? (y/N): ")
            if response.lower() != 'y':
                print("❌ 操作已取消")
                return False
            shutil.rmtree(extract_path)
            
        extract_path.mkdir(parents=True)
        
        # 解包CPIO
        try:
            # 在Windows上使用7-zip或者其他工具
            # 这里我们尝试使用Python的方式处理
            print(f"📦 正在解包 {cpio_path} 到 {extract_path}...")
            
            # 使用cpio命令解包 (需要安装cpio工具)
            cmd = f'cd "{extract_path}" && cpio -idmv < "{cpio_path.absolute()}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ CPIO解包成功!")
                print(f"📁 解包到目录: {extract_path}")
                return True
            else:
                print(f"❌ CPIO解包失败: {result.stderr}")
                # 尝试使用7-zip作为备选方案
                return self._extract_with_7zip(cpio_path, extract_path)
                
        except Exception as e:
            print(f"❌ 解包过程中出错: {e}")
            return self._extract_with_7zip(cpio_path, extract_path)
            
    def _extract_with_7zip(self, cpio_path, extract_path):
        """使用7-zip解包CPIO文件"""
        try:
            print("🔄 尝试使用7-zip解包...")
            cmd = f'7z x "{cpio_path}" -o"{extract_path}" -y'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ 7-zip解包成功!")
                return True
            else:
                print(f"❌ 7-zip解包失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 7-zip解包出错: {e}")
            return False
            
    def create_cpio(self, source_dir, output_file):
        """重新打包为CPIO文件"""
        source_path = Path(source_dir)
        output_path = Path(output_file)
        
        if not source_path.exists():
            print(f"❌ 错误: 源目录不存在: {source_path}")
            return False
            
        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 如果输出文件已存在，创建备份
        if output_path.exists():
            self.create_backup(output_path)
            
        try:
            print(f"📦 正在打包 {source_path} 为 {output_path}...")
            
            # 使用cpio命令打包
            cmd = f'cd "{source_path}" && find . | cpio -o -H newc > "{output_path.absolute()}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ CPIO打包成功!")
                new_hash = self.calculate_hash(output_path)
                print(f"📋 新文件MD5: {new_hash}")
                return True
            else:
                print(f"❌ CPIO打包失败: {result.stderr}")
                return self._create_with_7zip(source_path, output_path)
                
        except Exception as e:
            print(f"❌ 打包过程中出错: {e}")
            return self._create_with_7zip(source_path, output_path)
            
    def _create_with_7zip(self, source_path, output_path):
        """使用7-zip创建CPIO文件"""
        try:
            print("🔄 尝试使用7-zip打包...")
            # 7-zip创建cpio格式
            cmd = f'cd "{source_path}" && 7z a -tcpio "{output_path}" *'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ 7-zip打包成功!")
                return True
            else:
                print(f"❌ 7-zip打包失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 7-zip打包出错: {e}")
            return False
            
    def verify_integrity(self, original_dir, extracted_dir):
        """验证解包后的文件完整性"""
        print("🔍 验证文件完整性...")
        
        original_path = Path(original_dir)
        extracted_path = Path(extracted_dir)
        
        if not original_path.exists() or not extracted_path.exists():
            print("❌ 目录不存在，无法验证")
            return False
            
        # 比较文件数量和结构
        original_files = list(original_path.rglob('*'))
        extracted_files = list(extracted_path.rglob('*'))
        
        print(f"📊 原始目录文件数: {len(original_files)}")
        print(f"📊 解包目录文件数: {len(extracted_files)}")
        
        if len(original_files) == len(extracted_files):
            print("✓ 文件数量匹配")
            return True
        else:
            print("⚠️  文件数量不匹配，请检查")
            return False

def main():
    parser = argparse.ArgumentParser(description='Android Ramdisk CPIO 工具')
    parser.add_argument('action', choices=['extract', 'create', 'verify'], 
                       help='操作类型: extract(解包), create(打包), verify(验证)')
    parser.add_argument('-i', '--input', required=True, help='输入文件或目录')
    parser.add_argument('-o', '--output', help='输出文件或目录')
    parser.add_argument('--original', help='用于验证的原始目录')
    
    args = parser.parse_args()
    
    tool = RamdiskTool()
    
    if args.action == 'extract':
        if not args.output:
            args.output = Path(args.input).stem + '_extracted'
        success = tool.extract_cpio(args.input, args.output)
        if success:
            print(f"\n🎉 解包完成! 文件已解包到: {args.output}")
        else:
            print("\n❌ 解包失败!")
            
    elif args.action == 'create':
        if not args.output:
            args.output = Path(args.input).name + '.cpio'
        success = tool.create_cpio(args.input, args.output)
        if success:
            print(f"\n🎉 打包完成! 新文件: {args.output}")
        else:
            print("\n❌ 打包失败!")
            
    elif args.action == 'verify':
        if not args.original:
            print("❌ 验证操作需要指定 --original 参数")
            return
        success = tool.verify_integrity(args.original, args.input)
        if success:
            print("\n✓ 验证通过!")
        else:
            print("\n❌ 验证失败!")

if __name__ == '__main__':
    print("=" * 60)
    print("Android Ramdisk CPIO 工具")
    print("Copyright 2025 By.举个🌰")
    print("=" * 60)
    
    if len(sys.argv) == 1:
        print("\n使用示例:")
        print("解包: python ramdisk_tools.py extract -i recovery/ramdisk.cpio -o ramdisk_extracted")
        print("打包: python ramdisk_tools.py create -i ramdisk_extracted -o new_ramdisk.cpio")
        print("验证: python ramdisk_tools.py verify -i ramdisk_extracted --original recovery/ramdisk")
        print("\n使用 -h 参数查看详细帮助")
    else:
        main()
