#*********************************************************************************************************
#this file is used to store key map table
# custom_name:	  name of map table
# custom_code:	  custom code for remote device
# release_delay	  unit:ms.release will report from kernel to user layer after this period of time
#			  from press or repeat triggered.
###PARAS FOR MOUSE MODE:
# fn_key_scancode: scancode of fn key which used to swith mode
# cursor_left_scancode: scancode of left key
# cursor_right_scancode:  scancode of right key
# cursor_up_scancode: scancode of up key
# cursor_down_scancode: scancode of down key
# cursor_ok_scancode: scancode of ok key
#************************************************************************************************************* 
custom_name = amlogic-remote-4
custom_code = 0xdd22
release_delay = 80

key_begin	
	0xdc    179   #POWER
	0x9c    113   #VOLUME_MUTE
	0xcd    64   #F6 136
	0x91    65   #F7 137
	0x83    66   #F8 138
	0xc3    67   #F9 139
	0x86    167   #CHANNEL_DOWN
	0x85    166   #CHANNEL_UP
	0x81    114   #VOLUME_DOWN
	0x80    115   #VOLUME_UP
	0x8d    176   #SETTINGS
	0x82    125   #MENU
	0xce    97    #DPAD_CENTER
	0xca    103   #DPAD_UP
	0xd2    108   #DPAD_DOWN
	0x99    105   #DPAD_LEFT
	0xc1    106   #DPAD_RIGHT
	0x95    158   #BACK
	0x88    102   #HOME
	0x92    2     #1
	0x93    3     #2
	0xcc    4     #3
	0x8e    5     #4
	0x8f    6     #5
	0xc8    7     #6
	0x8a    8     #7
	0x8b    9     #8
	0xc4    10    #9
	0x87    11    #0
	0xd0    522   #NUMERIC_STAR
	0x5f    14    #DEL jiangsuYD
	0xc0    14    #DEL guangdongYD
	0xcb    14    #DEL 
	0xda    228   ##
	0xc9    175   #CAPTIONS
	0xd9    119   #MEDIA_PLAY_PAUSE
	0xf0    500   #MOBILE_M
	0xdd    92    #PAGE_UP
	0x8c    93    #PAGE_DOWN
	0xff    142   #F12
key_end