/(vendor|system/vendor)/bin/hw/rild		u:object_r:rild_exec:s0
/(vendor|system/vendor)/usr/idc(/.*)?		u:object_r:vendor_configs_file:s0
/(vendor|system/vendor)/bin/hw/hostapd		u:object_r:hal_wifi_hostapd_default_exec:s0
/(vendor|system/vendor)/usr/keylayout(/.*)?		u:object_r:vendor_configs_file:s0
/(vendor|system/vendor)/bin/bluetooth_init.sh		u:object_r:bluetooth_init_exec:s0
/(vendor|system/vendor)/bin/vndservicemanager		u:object_r:vndservicemanager_exec:s0
/(vendor|system/vendor)/bin/hw/wpa_supplicant		u:object_r:hal_wifi_supplicant_default_exec:s0
/(vendor|system/vendor)/lib(64)?/hw/gralloc\.default\.so		u:object_r:same_process_hal_file:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.ir@1\.0-service		u:object_r:hal_ir_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.vr@1\.0-service		u:object_r:hal_vr_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.drm@1\.0-service		u:object_r:hal_drm_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.cas@1\.0-service		u:object_r:hal_cas_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.nfc@1\.0-service		u:object_r:hal_nfc_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.nfc@1\.1-service		u:object_r:hal_nfc_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.usb@1\.0-service		u:object_r:hal_usb_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.boot@1\.0-service		u:object_r:hal_bootctl_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.gnss@1\.0-service		u:object_r:hal_gnss_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.wifi@1\.0-service		u:object_r:hal_wifi_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.audio@2\.0-service		u:object_r:hal_audio_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.light@2\.0-service		u:object_r:hal_light_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.power@1\.0-service		u:object_r:hal_power_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.health@1\.0-service		u:object_r:hal_health_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.health@2\.0-service		u:object_r:hal_health_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.lowpan@1\.0-service		u:object_r:hal_lowpan_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.tv\.cec@1\.0-service		u:object_r:hal_tv_cec_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.sensors@1\.0-service		u:object_r:hal_sensors_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.memtrack@1\.0-service		u:object_r:hal_memtrack_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.tv\.input@1\.0-service		u:object_r:hal_tv_input_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.vibrator@1\.0-service		u:object_r:hal_vibrator_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.bluetooth@1\.0-service		u:object_r:hal_bluetooth_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.dumpstate@1\.0-service		u:object_r:hal_dumpstate_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@4\.0-service		u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.keymaster@3\.0-service		u:object_r:hal_keymaster_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.media\.omx@1\.0-service		u:object_r:mediacodec_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.radio@1\.2-sap-service		u:object_r:hal_radio_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.contexthub@1\.0-service		u:object_r:hal_contexthub_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.gatekeeper@1\.0-service		u:object_r:hal_gatekeeper_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.thermal@1\.[01]-service		u:object_r:hal_thermal_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.radio@1\.2-radio-service		u:object_r:hal_radio_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.wifi\.offload@1\.0-service		u:object_r:hal_wifi_offload_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.radio\.config@1\.0-service		u:object_r:hal_radio_config_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.automotive\.evs@1\.0-service		u:object_r:hal_evs_default_exec:s0
/(vendor|sustem/vendor)/bin/hw/android\.hardware\.confirmationui@1\.0-service		u:object_r:hal_confirmationui_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.secure_element@1\.0-service		u:object_r:hal_secure_element_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.camera\.provider@2\.4-service		u:object_r:hal_camera_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.broadcastradio@\d+\.\d+-service		u:object_r:hal_broadcastradio_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.configstore@1\.[0-9]+-service		u:object_r:hal_configstore_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.graphics\.composer@2\.1-service		u:object_r:hal_graphics_composer_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.graphics\.composer@2\.2-service		u:object_r:hal_graphics_composer_default_exec:s0
/(vendor|system/vendor)/lib(64)?/hw/android\.hardware\.renderscript@1\.0-impl\.so		u:object_r:same_process_hal_file:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.automotive\.vehicle@2\.0-service		u:object_r:hal_vehicle_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.camera\.provider@2\.4-service_64		u:object_r:hal_camera_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.graphics\.allocator@2\.0-service		u:object_r:hal_graphics_allocator_default_exec:s0
/(vendor|system/vendor)/lib(64)?/hw/android\.hardware\.graphics\.mapper@2\.0-impl\.so		u:object_r:same_process_hal_file:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.biometrics\.fingerprint@2\.1-service		u:object_r:hal_fingerprint_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.automotive\.audiocontrol@1\.0-service		u:object_r:hal_audiocontrol_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.camera\.provider@2\.4-external-service		u:object_r:hal_camera_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.neuralnetworks@1\.1-service-ovx-driver		u:object_r:hal_neuralnetworks_default_exec:s0
/ctc(/.*)?		u:object_r:ctc_block_device:s0
/tee(/.*)?		u:object_r:tee_data_file:s0
/boot(/.*)?		u:object_r:boot_data_file:s0
/misc(/.*)?		u:object_r:misc_data_file:s0
/param(/.*)?		u:object_r:param_tv_file:s0
/backup(/.*)?		u:object_r:backup_block_device:s0
/dev/dvb.*		u:object_r:dvb_device:s0
/dev/amv.*		u:object_r:video_device:s0
/dev/vbi[0-3]		u:object_r:vbi_device:s0
/factory(/.*)?		u:object_r:factory_data_file:s0
/dev/dvb0.*		u:object_r:dvb_device:s0
/dev/ttyS[1-2]		u:object_r:hci_attach_dev:s0
/data/log(/.*)?		u:object_r:log_file:s0
/data/tee(/.*)?		u:object_r:tee_droid_data_file:s0
/dev/ttyUSB.*		u:object_r:radio_device:s0
/boot/optee.ko		u:object_r:optee_file:s0
/vendor/lib(64)?		u:object_r:same_process_hal_file:s0
/dev/hidraw[0-3]		u:object_r:hidraw_device:s0
/swap_zram0(/.*)?		u:object_r:swap_data_file:s0
/dev/hidraw[0-9]*		u:object_r:hidraw_audio_device:s0
/vendor/lib(64)?/libzvbi\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/extractors		u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libpppoe\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libam_mw\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libfbcnf\.so		u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libsubjni\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libam_adp\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libtv_jni\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libhdmicec\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libjnifont\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libamffmpeg\.so		u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libtvbinder\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libjniuevent\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libjnifont_tv\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libvendorfont\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libhdmicec_jni\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/liboptimization\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libicuuc_vendor\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libtvsubtitle_tv\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libjni_remoteime\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/jnidtvepgscanner\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libamadec_omx_api\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libjnidtvsubtitle\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libamffmpegadapter\.so		u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/gralloc\.amlogic\.so		u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libjnidtvepgscanner\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libtunertvinput_jni\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libsystemcontrol_jni\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libremotecontrol_jni\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libscreencontrol_jni\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libremotecontrolserver\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libscreencontrolclient\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libsystemcontrolclient\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/libsystemcontrolservice\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/extractors/libamextractor\.so		u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.hdmicec@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.tvserver@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.droidvold@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.screencontrol@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.systemcontrol@1\.1\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.systemcontrol@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.remotecontrol@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/vendor\.amlogic\.hardware\.subtitleserver@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/lib(64)?/hw/android\.hardware\.graphics\.mapper@2\.0-impl-2.1\.so		u:object_r:same_process_hal_file:s0
/acct/cgroup.procs		u:object_r:reco_file:s0
/dev/amaudio_.*		u:object_r:aml_audio_device:s0
/dev/block/sd[a-z]		u:object_r:sda_block_device:s0
/dev/block/sd[a-z](.*)		u:object_r:sda_block_device:s0
/dev/amstream_.*		u:object_r:codec_device:s0
/sys/class/stb(/.*)?		u:object_r:sysfs_stb:s0
/data/droidota(/.*)?		u:object_r:update_data_file:s0
/mnt/vendor/tee(/.*)?		u:object_r:tee_data_file:s0
/dev/block/vold(/.*)?		u:object_r:vold_block_device:s0
/acct/uid/cgroup.procs		u:object_r:reco_file:s0
/sys/class/video(/.*)?		u:object_r:sysfs_video:s0
/system/bin/fsck.exfat		u:object_r:fsck_exec:s0
/acct/uid_0/pid_.*/cgroup.procs		u:object_r:reco_file:s0
/dev/block/mmcblk[0-9]		u:object_r:sda_block_device:s0
/sys/class/amvecm(/.*)?		u:object_r:sysfs_video:s0
/mnt/vendor/param(/.*)?		u:object_r:param_tv_file:s0
/data/vendor/hdcp(/.*)?		u:object_r:hdcp_file:s0
/dev/block/mmcblk[0-9]rpmb		u:object_r:sda_block_device:s0
/dev/block/mmcblk[0-9]p(.*)		u:object_r:sda_block_device:s0
/sys/class/display(/.*)		u:object_r:sysfs_display:s0
/acct/uid_0/cgroup.procs		u:object_r:reco_file:s0
/sys/class/astream(/.*)?		u:object_r:sysfs_audio:s0
/data/vendor/btmic(/.*)?		u:object_r:btmic_data_file:s0
/dev/video_composer.0		u:object_r:vendor_video_device:s0
/dev/video_composer.1		u:object_r:vendor_video_device:s0
/vendor/bin/swiperf.sh		u:object_r:swiperf_setup_exec:s0
/sys/class/graphics(/.*)?		u:object_r:sysfs_display:s0
/mnt/vendor/factory(/.*)?		u:object_r:factory_data_file:s0
/sys/devices/bt-dev.*/rfkill/rfkill0/type		u:object_r:sysfs_bluetooth_writable:s0
/sys/devices/bt-dev.*/rfkill/rfkill0/state		u:object_r:sysfs_bluetooth_writable:s0
/sys/class/unifykeys(/.*)?		u:object_r:sysfs_unifykey:s0
/data/vendor/mediadrm(/.*)?		u:object_r:hal_drm_data:s0
/data/vendor/wifi/wpa(/.*)?		u:object_r:wpa_data_file:s0
/system/bin/logcapture.sh		u:object_r:logcapture_exec:s0
/vendor/etc/filteredid.cfg		u:object_r:vendor_app_file:s0
/mnt/vendor/swap_zram0(/.*)?		u:object_r:swap_data_file:s0
/sys/class/android_usb(/.*)?		u:object_r:sysfs_usb:s0
/vendor/etc/mesondisplay.cfg		u:object_r:vendor_app_file:s0
/sys/class/dual_role_usb(/.*)?		u:object_r:sysfs_usb:s0
/sys/module/di/parameters(/.*)?		u:object_r:sysfs_di:s0
/data/vendor/wifi/hostapd(/.*)?		u:object_r:hostapd_data_file:s0
/vendor/bin/init_sunniwell.sh		u:object_r:sunniwell_setup_exec:s0
/acct/uid_0/pid_2161/cgroup.procs		u:object_r:reco_file:s0
/system/bin/andlink_para_init.sh		u:object_r:andlinkservice_exec:s0
/sys/module/amvideo/parameters(/.*)?		u:object_r:sysfs_video:s0
/sys/module/am_vecm/parameters(/.*)?		u:object_r:sysfs_am_vecm:s0
/sys/devices/platform/ffe40000.bifrost/mem_pool_size		u:object_r:sysfs_mali:s0
/sys/devices/platform/ffd26000.hdmirx/hdmirx/hdmirx0/key		u:object_r:sysfs_unifykey:s0
/sys/devices/platform/ffd26000.hdmirx/hdmirx/hdmirx0/edid		u:object_r:sysfs_cec:s0
/sys/devices/platform/fb/graphics(/.*)?		u:object_r:sysfs_display:s0
/sys/devices/meson-fb/graphics/fb[0-3](/.*)		u:object_r:sysfs_display:s0
/sys/module/tvin_hdmirx/parameters(/.*)?		u:object_r:sysfs_cec:s0
/sys/devices/virtual/remote/amremote(/.*)?		u:object_r:sysfs_remote:s0
/sys/devices/platform/fb/graphics/fb[0-3](/.*)		u:object_r:sysfs_display:s0
/sys/module/amdolby_vision/parameters(/.*)?		u:object_r:sysfs_video:s0
/sys/devices/virtual/amhdmitx/amhdmitx0(/.*)		u:object_r:sysfs_cec:s0
/sys/devices/platform/meson-fb/graphics(/.*)?		u:object_r:sysfs_display:s0
/sys/devices/virtual/amhdmitx/amhdmitx0(/.*)?		u:object_r:sysfs_amhdmitx:s0
/sys/devices/platform/meson-fb/graphics/fb[0-3](/.*)		u:object_r:sysfs_display:s0
/sys/class/astream/astream-dev/uio0/maps/map0(/.*)?		u:object_r:sysfs_audio:s0
/sys/devices/virtual/meson-irblaster/irblaster1(/.*)?		u:object_r:sysfs_ir:s0
/sys/devices/platform/vout/extcon/setmode/cable.0/state		u:object_r:sysfs_display:s0
/vendor/bin/hw/android\.hardware\.drm@1\.1-service.widevine		u:object_r:hal_drm_default_exec:s0
/sys/devices/virtual/astream/astream-dev/subsystem(/.*)?		u:object_r:sysfs_audio:s0
/vendor/bin/hw/android\.hardware\.dumpstate@1\.0-service.droidlogic		u:object_r:hal_dumpstate_default_exec:s0
/sys/devices/virtual/astream/astream-dev/uio0/maps/map0(/.*)?		u:object_r:sysfs_audio:s0
/sys/devices/platform/dummy-battery/power_supply/Battery(/.*)		u:object_r:sysfs_power:s0
/dev/vbi		u:object_r:vbi_device:s0
/dev/esm		u:object_r:hdcptx_device:s0
/dev/uvm		u:object_r:gpu_device:s0
/dev/cec		u:object_r:cec_device:s0
/dev/dtb		u:object_r:dtb_device:s0
/dev/di0		u:object_r:di0_device:s0
/dev/tee0		u:object_r:drm_device:s0
/dev/mali		u:object_r:gpu_device:s0
/dev/ge2d		u:object_r:ge2d_device:s0
/dev/vdin1		u:object_r:video_device:s0
/dev/vdin0		u:object_r:video_device:s0
/dev/mali0		u:object_r:gpu_device:s0
/dev/stpbt		u:object_r:device_stpbt:s0
/mnt/vendor		u:object_r:param_tv_file:s0
/dev/tvafe0		u:object_r:video_device:s0
/dev/picdec		u:object_r:picture_device:s0
/dev/esm_rx		u:object_r:hdcprx_device:s0
/dev/btusb0		u:object_r:hci_attach_dev:s0
/dev/ttyBT0		u:object_r:device_stpbt:s0
/dev/ttyBT1		u:object_r:device_stpbt:s0
/dev/amvecm		u:object_r:amvecm_device:s0
/dev/galcore		u:object_r:galcore_device:s0
/dev/sw_sync		u:object_r:sw_sync_device:s0
/dev/bt_usb0		u:object_r:hci_attach_dev:s0
/dev/hdmirx0		u:object_r:hdmirx0_device:s0
/dev/display		u:object_r:display_device:s0
/dev/amvideo		u:object_r:vendor_video_device:s0
/dev/HevcEnc		u:object_r:video_device:s0
/dev/teepriv0		u:object_r:drm_device:s0
/dev/nand_env		u:object_r:env_device:s0
/dev/display2		u:object_r:display_device:s0
/dev/ac_sbuf0		u:object_r:video_device:s0
/dev/ionvideo		u:object_r:video_device:s0
/dev/v4lvideo		u:object_r:video_device:s0
/dev/amremote		u:object_r:input_device:s0
/dev/rtk_btusb		u:object_r:hci_attach_dev:s0
/dev/rtkbt_dev		u:object_r:hci_attach_dev:s0
/dev/defendkey		u:object_r:defendkey_device:s0
/dev/block/tee		u:object_r:tee_block_device:s0
/dev/block/odm		u:object_r:odm_block_device:s0
/dev/block/drm		u:object_r:drm_block_device:s0
/dev/block/env		u:object_r:env_device:s0
/dev/ac_isp4uf		u:object_r:video_device:s0
/dev/unifykeys		u:object_r:unify_device:s0
/dev/videosync		u:object_r:video_device:s0
/dev/wifi_power		u:object_r:radio_device:s0
/dev/socket/dig		u:object_r:dig_socket:s0
/dev/otz_client		u:object_r:tee_device:s0
/dev/bootloader		u:object_r:bootloader_device:s0
/dev/block/dtbo		u:object_r:dtbo_block_device:s0
/dev/block/misc		u:object_r:misc_block_device:s0
/dev/block/boot		u:object_r:boot_block_device:s0
/dev/block/data		u:object_r:userdata_block_device:s0
/dev/am_adc_kpd		u:object_r:input_device:s0
/ctc/bin/amtprox		u:object_r:amtprox_exec:s0
/dev/videotunnel		u:object_r:gpu_device:s0
/dev/block/odm_a		u:object_r:odm_block_device:s0
/dev/block/odm_b		u:object_r:odm_block_device:s0
/dev/block/zram0		u:object_r:swap_block_device:s0
/dev/block/param		u:object_r:param_block_device:s0
/dev/block/cache		u:object_r:cache_block_device:s0
/dev/avin_detect		u:object_r:avin_device:s0
/dev/graphics/fb0		u:object_r:sysfs_display:s0
/dev/graphics/fb1		u:object_r:sysfs_display:s0
/system/bin/iptvd		u:object_r:iptvd_exec:s0
/dev/block/backup		u:object_r:backup_block_device:s0
/dev/block/vbmeta		u:object_r:vbmeta_block_device:s0
/dev/block/dtbo_a		u:object_r:dtbo_block_device:s0
/dev/block/dtbo_b		u:object_r:dtbo_block_device:s0
/dev/block/system		u:object_r:system_block_fsck_device:s0
/dev/block/vendor		u:object_r:vendor_block_device:s0
/dev/block/boot_b		u:object_r:boot_block_device:s0
/dev/block/boot_a		u:object_r:boot_block_device:s0
/dev/amstream_sub		u:object_r:subtitle_device:s0
/dev/amvideo_poll		u:object_r:video_device:s0
/dev/amaudio2_out		u:object_r:aml_audio_device:s0
/vendor/xbin/bcmdl		u:object_r:bcmdl_exec:s0
/sys/class/vfm/map		u:object_r:sysfs_video:s0
/dev/v4l2_frontend		u:object_r:frontend_device:s0
/dev/ddr_parameter		u:object_r:dtb_device:s0
/dev/block/product		u:object_r:product_block_device:s0
/system/bin/ntfs-3g		u:object_r:fsck_exec:s0
/system/bin/ntfsfix		u:object_r:fsck_exec:s0
/dev/block/vbmeta_a		u:object_r:vbmeta_block_device:s0
/dev/block/vbmeta_b		u:object_r:vbmeta_block_device:s0
/dev/block/recovery		u:object_r:recovery_block_device:s0
/dev/block/vendor_b		u:object_r:vendor_block_device:s0
/dev/block/system_a		u:object_r:system_block_fsck_device:s0
/dev/block/system_b		u:object_r:system_block_fsck_device:s0
/dev/block/vendor_a		u:object_r:vendor_block_device:s0
/dev/block/cri_data		u:object_r:cri_block_device:s0
/dev/block/metadata		u:object_r:metadata_block_device:s0
/vendor/bin/hdmicecd		u:object_r:hdmicecd_exec:s0
/vendor/bin/tee_hdcp		u:object_r:tee_exec:s0
/vendor/bin/tvserver		u:object_r:tvserver_exec:s0
/ctc/bin/IptvService		u:object_r:IptvService_exec:s0
/sys/power/boot_type		u:object_r:sysfs_boottype:s0
/dev/block/product_b		u:object_r:product_block_device:s0
/dev/block/product_a		u:object_r:product_block_device:s0
/system/bin/chservice		u:object_r:chservice_exec:s0
/vendor/bin/droidvold		u:object_r:droidvold_exec:s0
/vendor/bin/rc_server		u:object_r:rc_server_exec:s0
/vendor/bin/hdcp_rx22		u:object_r:hdcp_rx22_exec:s0
/vendor/bin/hdcp_tx22		u:object_r:hdcp_tx22_exec:s0
/vendor/bin/hdcp_rp22		u:object_r:hdcp_rp22_exec:s0
/vendor/bin/remotecfg		u:object_r:remotecfg_exec:s0
/sys/class/tsync/mode		u:object_r:sysfs_xbmc:s0
/sys/class/lcd/enable		u:object_r:sysfs_lcd:s0
/sys/class/video/crop		u:object_r:sysfs_video:s0
/sys/class/video/axis		u:object_r:sysfs_video:s0
/dev/block/bootloader		u:object_r:bootloader_device:s0
/dev/block/metadata_a		u:object_r:metadata_block_device:s0
/dev/block/metadata_b		u:object_r:metadata_block_device:s0
/dev/audio_data_debug		u:object_r:aml_audio_device:s0
/vendor/bin/thermalcfg		u:object_r:thermalcfg_exec:s0
/sys/class/tsync/event		u:object_r:sysfs_xbmc:s0
/sys/class/ppmgr/angle		u:object_r:sysfs_video:s0
/dev/amstream_sub_read		u:object_r:subtitle_device:s0
/data/media_rw/sdcard1		u:object_r:media_rw_data_file:s0
/sys/class/display/mode		u:object_r:sysfs_display:s0
/system/bin/xiriservice		u:object_r:xiriservice_exec:s0
/system/bin/imageserver		u:object_r:imageserver_exec:s0
/sys/class/cec/port_seq		u:object_r:sysfs_cec:s0
/sys/class/tsync/enable		u:object_r:sysfs_video:s0
/dev/block/mmcblk0boot1		u:object_r:bootloader_device:s0
/dev/block/mmcblk0boot0		u:object_r:bootloader_device:s0
/data/misc/wifi/sockets		u:object_r:wifi_socket:s0
/sys/class/display2/mode		u:object_r:sysfs_display:s0
/vendor/bin/wifi_preload		u:object_r:wifi_preload_exec:s0
/vendor/bin/dtvkitserver		u:object_r:dtvkitserver_exec:s0
/sys/class/stb/audio_pts		u:object_r:sysfs_xbmc:s0
/sys/class/stb/video_pts		u:object_r:sysfs_xbmc:s0
/sys/class/amaudio/debug		u:object_r:sysfs_audio:s0
/vendor/bin/systemcontrol		u:object_r:system_control_exec:s0
/vendor/bin/swRootService		u:object_r:swRootService_exec:s0
/sys/class/mpgpu/mpgpucmd		u:object_r:sysfs_mpgpu_cmd:s0
/sys/class/video/crop_pip		u:object_r:sysfs_video:s0
/sys/class/video/axis_pip		u:object_r:sysfs_video:s0
/dev/socket/pppoe_wrapper		u:object_r:pppoe_wrapper_socket:s0
/vendor/bin/tee-supplicant		u:object_r:tee_exec:s0
/vendor/bin/tee_preload_fw		u:object_r:firmload_exec:s0
/vendor/bin/miracast_hdcp2		u:object_r:miracast_hdcp2_exec:s0
/sys/class/tsync/firstapts		u:object_r:sysfs_xbmc:s0
/sys/class/tsync/pts_audio		u:object_r:sysfs_xbmc:s0
/sys/class/tsync/pts_video		u:object_r:sysfs_xbmc:s0
/sys/class/tsync/firstvpts		u:object_r:sysfs_xbmc:s0
/system/bin/RootShellServer		u:object_r:RootShellServer_exec:s0
/sys/class/mpgpu/scale_mode		u:object_r:sysfs_mpgpu_scale:s0
/sys/class/tsync/pts_pcrscr		u:object_r:sysfs_xbmc:s0
/sys/class/tsync/vpause_flag		u:object_r:ctsplayer_file:s0
/vendor/bin/hw/subtitleserver		u:object_r:subtitleserver_exec:s0
/sys/class/dmx/demux0_scramble		u:object_r:sysfs_audio:s0
/sys/class/hdmirx/hdmirx0/edid		u:object_r:sysfs_cec:s0
/sys/class/video/disable_video		u:object_r:sysfs_video:s0
/sys/class/amaudio/mute_unmute		u:object_r:sysfs_audio:s0
/sys/class/audiodsp/digital_raw		u:object_r:sysfs_audio:s0
/sys/power/early_suspend_trigger		u:object_r:sysfs_power_trigger:s0
/sys/class/defendkey/decrypt_dtb		u:object_r:sysfs_defendkey:s0
/sys/class/tsync/av_threshold_min		u:object_r:ctsplayer_file:s0
/sys/class/audiodsp/digital_codec		u:object_r:sysfs_digital_codec:s0
/sys/class/aml_store/store_device		u:object_r:sysfs_store:s0
/sys/class/aml_store/bl_off_bytes		u:object_r:sysfs_store:s0
/sys/class/video/disable_videopip		u:object_r:sysfs_video:s0
/sys/class/tsync/checkin_firstapts		u:object_r:sysfs_xbmc:s0
/sys/class/tsync/checkin_firstvpts		u:object_r:sysfs_xbmc:s0
/sys/module/rdma/parameters/enable		u:object_r:sysfs_rdma:s0
/sys/class/video/video_layer1_state		u:object_r:sysfs_display:s0
/sys/class/tsync_pcr/tsync_pcr_mode		u:object_r:sysfs_xbmc:s0
/sys/module/di/parameters/bypass_all		u:object_r:sysfs_mpgpu_scale:s0
/sys/class/audiodsp/audio_samesource		u:object_r:sysfs_audio_samesource:s0
/sys/class/amhdmitx/amhdmitx0/avmute		u:object_r:sysfs_amhdmitx:s0
/sys/class/thermal/thermal_zone0/temp		u:object_r:sysfs_thermal:s0
/sys/class/amhdmitx/amhdmitx0/hdr_cap		u:object_r:sysfs_amhdmitx:s0
/sys/class/amaudio/audio_channels_mask		u:object_r:ctsplayer_file:s0
/sys/class/video/video_scaler_path_sel		u:object_r:sysfs_video:s0
/sys/class/amhdmitx/amhdmitx0/disp_cap		u:object_r:sysfs_amhdmitx:s0
/sys/block/mmcblk0/queue/read_ahead_kb		u:object_r:sysfs_block_ahead:s0
/sys/class/amhdmitx/amhdmitx0/hdcp_mode		u:object_r:sysfs_amhdmitx:s0
/sys/class/amhdmitx/amhdmitx0/sink_type		u:object_r:sysfs_amhdmitx:s0
/sys/module/fb/parameters/osd_logo_index		u:object_r:sysfs_display:s0
/sys/module/dhd/parameters/firmware_path		u:object_r:sysfs_wifi:s0
/sys/devices/platform/rtc/rtc/rtc0/hctosys		u:object_r:sysfs_rtc:s0
/sys/module/firmware_class/parameters/path		u:object_r:sysfs_video:s0
/sys/class/amhdmitx/amhdmitx0/edid_parsing		u:object_r:sysfs_amhdmitx:s0
/sys/devices/platform/aml_pm/suspend_reason		u:object_r:sysfs_pm:s0
/sys/module/bcmdhd/parameters/firmware_path		u:object_r:sysfs_wifi:s0
/sys/module/tvin_afe/parameters/force_nostd		u:object_r:sysfs_video:s0
/sys/devices/platform/vout/extcon/setmode/state		u:object_r:sysfs_display:s0
/sys/devices/virtual/thermal/thermal_zone0/mode		u:object_r:sysfs_display:s0
/sys/devices/virtual/amhdmitx/amhdmitx0/aud_cap		u:object_r:sysfs_audio_cap:s0
/sys/devices/platform/fb/graphics/fb0/osd_afbcd		u:object_r:sysfs_fb0_afbcd:s0
/sys/devices/platform/bt-dev/rfkill/rfkill0/type		u:object_r:sysfs_bluetooth_writable:s0
/sys/devices/virtual/amhdmitx/amhdmitx0/edid_info		u:object_r:sysfs_cec:s0
/sys/devices/platform/bt-dev/rfkill/rfkill0/state		u:object_r:sysfs_bluetooth_writable:s0
/sys/module/amvdec_h265/parameters/double_write_mode		u:object_r:sysfs_amvdec:s0
/sys/devices/platform/meson-fb/graphics/fb0/osd_afbcd		u:object_r:sysfs_fb0_afbcd:s0
/sys/module/amvdec_h264/parameters/error_recovery_mode		u:object_r:ctsplayer_file:s0
/sys/devices/virtual/amhdmitx/amhdmitx0/aud_output_chs		u:object_r:sysfs_aud_output_chs:s0
/vendor/lib/vendor\.amlogic\.hardware\.remotecontrol@1\.0\.so		u:object_r:vendor_app_file:s0
/vendor/bin/hw/android\.hardware\.drm@1\.1-service\.clearkey		u:object_r:hal_drm_clearkey_exec:s0
/sys/devices/virtual/amhdmitx/amhdmitx0/hdmi_audio/state		u:object_r:sysfs_hdmi:s0
/sys/module/decoder_common/parameters/force_nosecure_even_drm		u:object_r:sysfs_vdec:s0
/sys/devices/system/clocksource/clocksource0/current_clocksource		u:object_r:sysfs_clock:s0
