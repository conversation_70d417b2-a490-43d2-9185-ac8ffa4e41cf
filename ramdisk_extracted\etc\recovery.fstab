#device    mountpoint    fstype    options    flags?    (fstab    version    2)
/dev/block/bootloader   /bootloader emmc    defaults    defaults
/dev/block/logo         /logo       emmc    defaults    defaults
/dev/block/vbmeta       /vbmeta       emmc    defaults    defaults
/dev/block/recovery     /recovery   emmc    defaults    defaults
/dev/block/boot         /boot   emmc    defaults    defaults
/dev/block/misc         /misc   emmc    defaults    defaults
/dev/block/system       /system   ext4    defaults,ro    defaults
/dev/block/vendor       /vendor ext4    defaults,ro    defaults
/dev/block/odm          /odm    ext4    defaults    defaults
/dev/block/param        /param    ext4    defaults    defaults
/dev/block/product      /product    ext4    defaults    defaults
/dev/block/metadata     /metadata    ext4    defaults    defaults
/dev/block/tee          /tee    ext4    defaults    defaults
/dev/block/cache        /cache  ext4    defaults    defaults
/dev/block/backup       /backup  ext4    defaults    defaults
/dev/block/data         /data   ext4    defaults    encryptable=footer
/dev/block/mmcblk1p1    /sdcard vfat    defaults    defaults
/dev/block/mmcblk1p2    /sdcard vfat    defaults    defaults
/dev/block/mmcblk1p3    /sdcard vfat    defaults    defaults
/dev/block/mmcblk1p4    /sdcard vfat    defaults    defaults
/dev/block/mmcblk1p5    /sdcard vfat    defaults    defaults
/dev/block/mmcblk1p6    /sdcard vfat    defaults    defaults
/dev/block/mmcblk2p1    /sdcard vfat    defaults    defaults
/dev/block/mmcblk2p2    /sdcard vfat    defaults    defaults
/dev/block/mmcblk2p3    /sdcard vfat    defaults    defaults
/dev/block/mmcblk2p4    /sdcard vfat    defaults    defaults
/dev/block/mmcblk2p5    /sdcard vfat    defaults    defaults
/dev/block/mmcblk2p6    /sdcard vfat    defaults    defaults
/dev/block/mmcblk    /sdcard vfat    defaults    defaults
/dev/block/sd##         /udisk  auto    defaults    defaults
