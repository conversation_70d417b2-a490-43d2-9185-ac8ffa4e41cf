/(odm|vendor/odm)(/.*)?		u:object_r:vendor_file:s0
/(odm|vendor/odm)/bin/sh		u:object_r:vendor_shell_exec:s0
/(odm|vendor/odm)/etc(/.*)?		u:object_r:vendor_configs_file:s0
/(odm|vendor/odm)/app(/.*)?		u:object_r:vendor_app_file:s0
/(odm|vendor/odm)/lib(64)?/hw		u:object_r:vendor_hal_file:s0
/(vendor|system/vendor)(/.*)?		u:object_r:vendor_file:s0
/(vendor|system/vendor)/bin/sh		u:object_r:vendor_shell_exec:s0
/(product|system/product)(/.*)?		u:object_r:system_file:s0
/(odm|vendor/odm)/overlay(/.*)?		u:object_r:vendor_overlay_file:s0
/(odm|vendor/odm)/priv-app(/.*)?		u:object_r:vendor_app_file:s0
/(odm|vendor/odm)/framework(/.*)?		u:object_r:vendor_framework_file:s0
/(vendor|system/vendor)/etc(/.*)?		u:object_r:vendor_configs_file:s0
/(vendor|system/vendor)/app(/.*)?		u:object_r:vendor_app_file:s0
/(vendor|system/vendor)/bin/toolbox		u:object_r:vendor_toolbox_exec:s0
/(vendor|system/vendor)/lib(64)?/hw		u:object_r:vendor_hal_file:s0
/(odm|vendor/odm)/lib(64)?/egl(/.*)?		u:object_r:same_process_hal_file:s0
/(vendor|system/vendor)/manifest.xml		u:object_r:vendor_configs_file:s0
/(vendor|system/vendor)/overlay(/.*)?		u:object_r:vendor_overlay_file:s0
/(vendor|system/vendor)/priv-app(/.*)?		u:object_r:vendor_app_file:s0
/(vendor|system/vendor)/etc/vintf(/.*)?		u:object_r:vendor_configs_file:s0
/(vendor|system/vendor)/framework(/.*)?		u:object_r:vendor_framework_file:s0
/(odm|vendor/odm)/lib(64)?/vndk-sp(/.*)?		u:object_r:vndk_sp_file:s0
/(vendor|system/vendor)/bin/toybox_vendor		u:object_r:vendor_toolbox_exec:s0
/(vendor|system/vendor)/lib(64)?/egl(/.*)?		u:object_r:same_process_hal_file:s0
/(odm|vendor/odm)/etc/selinux/odm_sepolicy.cil		u:object_r:sepolicy_file:s0
/(vendor|system/vendor)/lib(64)?/vndk-sp(/.*)?		u:object_r:vndk_sp_file:s0
/(odm|vendor/odm)/etc/selinux/odm_file_contexts		u:object_r:file_contexts_file:s0
/(odm|vendor/odm)/etc/selinux/odm_seapp_contexts		u:object_r:seapp_contexts_file:s0
/(vendor|system/vendor)/compatibility_matrix.xml		u:object_r:vendor_configs_file:s0
/(odm|vendor/odm)/etc/selinux/odm_property_contexts		u:object_r:property_contexts_file:s0
/(odm|vendor/odm)/etc/selinux/odm_hwservice_contexts		u:object_r:hwservice_contexts_file:s0
/(odm|vendor/odm)/etc/selinux/odm_mac_permissions.xml		u:object_r:mac_perms_file:s0
/res(/.*)?		u:object_r:rootfs:s0
/dev(/.*)?		u:object_r:device:s0
/lib(/.*)?		u:object_r:rootfs:s0
/efs(/.*)?		u:object_r:efs_file:s0
/oem(/.*)?		u:object_r:oemfs:s0
/sbin(/.*)?		u:object_r:rootfs:s0
/data(/.*)?		u:object_r:system_data_file:s0
/init\..*		u:object_r:rootfs:s0
/cores(/.*)?		u:object_r:coredump_file:s0
/cache(/.*)?		u:object_r:cache_file:s0
/data/.layout_version		u:object_r:install_data_file:s0
/fstab\..*		u:object_r:rootfs:s0
/system(/.*)?		u:object_r:system_file:s0
/dev/rtc[0-9]		u:object_r:rtc_device:s0
/dev/tty[0-9]*		u:object_r:tty_device:s0
/dev/uio[0-9]*		u:object_r:uio_device:s0
/dev/mtd(/.*)?		u:object_r:mtd_device:s0
/dev/snd(/.*)?		u:object_r:audio_device:s0
/dev/adf[0-9]*		u:object_r:graphics_device:s0
/storage(/.*)?		u:object_r:storage_file:s0
/dev/vcs[0-9a-z]*		u:object_r:vcs_device:s0
/ueventd\..*		u:object_r:rootfs:s0
/dev/ttyS[0-9]*		u:object_r:serial_device:s0
/dev/i2c-[0-9]+		u:object_r:i2c_device:s0
/metadata(/.*)?		u:object_r:metadata_file:s0
/mnt/asec(/.*)?		u:object_r:asec_apk_file:s0
/mnt/user(/.*)?		u:object_r:mnt_user_file:s0
/data/app(/.*)?		u:object_r:apk_data_file:s0
/data/drm(/.*)?		u:object_r:drm_data_file:s0
/data/ota(/.*)?		u:object_r:ota_data_file:s0
/data/adb(/.*)?		u:object_r:adb_data_file:s0
/data/anr(/.*)?		u:object_r:anr_data_file:s0
/dev/tegra.*		u:object_r:video_device:s0
/dev/modem.*		u:object_r:radio_device:s0
/dev/audio.*		u:object_r:audio_device:s0
/dev/video[0-9]*		u:object_r:video_device:s0
/dev/memcg(/.*)?		u:object_r:cgroup:s0
/dev/input(/.*)?		u:object_r:input_device:s0
/dev/block(/.*)?		u:object_r:block_device:s0
/mnt/asec/[^/]+/[^/]+\.zip		u:object_r:asec_public_file:s0
/mnt/asec/[^/]+/lib(/.*)?		u:object_r:asec_public_file:s0
/data/app/[^/]+/oat(/.*)?		u:object_r:dalvikcache_data_file:s0
/dev/cpuctl(/.*)?		u:object_r:cpuctl_device:s0
/dev/socket(/.*)?		u:object_r:socket_device:s0
/data/cache(/.*)?		u:object_r:cache_file:s0
/mnt/vendor(/.*)?		u:object_r:mnt_vendor_file:s0
/mnt/expand(/.*)?		u:object_r:mnt_expand_file:s0
/data/media(/.*)?		u:object_r:media_rw_data_file:s0
/dev/akm8973.*		u:object_r:sensors_device:s0
/dev/bus/usb(.*)?		u:object_r:usb_device:s0
/mnt/runtime(/.*)?		u:object_r:storage_file:s0
/data/vendor(/.*)?		u:object_r:vendor_data_file:s0
/data/backup(/.*)?		u:object_r:backup_data_file:s0
/mnt/expand/[^/]+(/.*)?		u:object_r:system_data_file:s0
/mnt/expand/[^/]+/app(/.*)?		u:object_r:apk_data_file:s0
/mnt/expand/[^/]+/media(/.*)?		u:object_r:media_rw_data_file:s0
/mnt/expand/[^/]+/local/tmp(/.*)?		u:object_r:shell_data_file:s0
/mnt/expand/[^/]+/misc/vold(/.*)?		u:object_r:vold_data_file:s0
/mnt/expand/[^/]+/app/[^/]+/oat(/.*)?		u:object_r:dalvikcache_data_file:s0
/mnt/expand/[^/]+/app/vmdl[^/]+\.tmp(/.*)?		u:object_r:apk_tmp_file:s0
/mnt/expand/[^/]+/app/vmdl[^/]+\.tmp/oat(/.*)?		u:object_r:dalvikcache_data_file:s0
/dev/fscklogs(/.*)?		u:object_r:fscklogs:s0
/dev/graphics(/.*)?		u:object_r:graphics_device:s0
/mnt/media_rw(/.*)?		u:object_r:mnt_media_rw_file:s0
/cache/backup(/.*)?		u:object_r:cache_private_backup_file:s0
/dev/spdif_out.*		u:object_r:audio_device:s0
/dev/rpmsg-omx[0-9]		u:object_r:rpmsg_device:s0
/dev/v4l-touch[0-9]*		u:object_r:input_device:s0
/dev/block/ram[0-9]*		u:object_r:ram_device:s0
/dev/block/dm-[0-9]+		u:object_r:dm_device:s0
/data/app-asec(/.*)?		u:object_r:asec_image_file:s0
/metadata/vold(/.*)?		u:object_r:vold_metadata_file:s0
/data/misc/net(/.*)?		u:object_r:net_data_file:s0
/data/misc/vpn(/.*)?		u:object_r:vpn_data_file:s0
/data/misc/sms(/.*)?		u:object_r:radio_data_file:s0
/data/preloads(/.*)?		u:object_r:preloads_data_file:s0
/data/misc/adb(/.*)?		u:object_r:adb_keys_file:s0
/data/mediadrm(/.*)?		u:object_r:media_data_file:s0
/data/property(/.*)?		u:object_r:property_data_file:s0
/data/app/vmdl[^/]+\.tmp(/.*)?		u:object_r:apk_tmp_file:s0
/data/misc_de/[0-9]+/vold(/.*)?		u:object_r:vold_data_file:s0
/data/misc_ce/[0-9]+/vold(/.*)?		u:object_r:vold_data_file:s0
/data/app/vmdl[^/]+\.tmp/oat(/.*)?		u:object_r:dalvikcache_data_file:s0
/data/misc_de/[0-9]+/storaged(/.*)?		u:object_r:storaged_data_file:s0
/data/misc_ce/[0-9]+/storaged(/.*)?		u:object_r:storaged_data_file:s0
/dev/iio:device[0-9]+		u:object_r:iio_device:s0
/dev/block/zram[0-9]*		u:object_r:ram_device:s0
/dev/block/loop[0-9]*		u:object_r:loop_device:s0
/cache/recovery(/.*)?		u:object_r:cache_recovery_file:s0
/data/vendor_ce(/.*)?		u:object_r:vendor_data_file:s0
/data/vendor_de(/.*)?		u:object_r:vendor_data_file:s0
/data/bootchart(/.*)?		u:object_r:bootchart_data_file:s0
/data/misc/vold(/.*)?		u:object_r:vold_data_file:s0
/data/misc/user(/.*)?		u:object_r:misc_user_data_file:s0
/data/misc/wifi(/.*)?		u:object_r:wifi_data_file:s0
/data/misc/dhcp(/.*)?		u:object_r:dhcp_data_file:s0
/data/misc/logd(/.*)?		u:object_r:misc_logd_file:s0
/data/misc/apns(/.*)?		u:object_r:radio_data_file:s0
/data/local/tmp(/.*)?		u:object_r:shell_data_file:s0
/dev/block/vold/.+		u:object_r:vold_device:s0
/data/misc/trace(/.*)?		u:object_r:method_trace_data_file:s0
/data/misc/media(/.*)?		u:object_r:media_data_file:s0
/data/misc/audio(/.*)?		u:object_r:audio_data_file:s0
/data/nativetest(/.*)?		u:object_r:nativetest_data_file:s0
/data/tombstones(/.*)?		u:object_r:tombstone_data_file:s0
/data/vendor_de/[0-9]+/fpdata(/.*)?		u:object_r:fingerprint_vendor_data_file:s0
/data/system_de/[0-9]+/ringtones(/.*)?		u:object_r:ringtone_file:s0
/data/system_ce/[0-9]+/shortcut_service/bitmaps(/.*)?		u:object_r:shortcut_manager_icons:s0
/data/misc/camera(/.*)?		u:object_r:camera_data_file:s0
/data/app-private(/.*)?		u:object_r:apk_private_data_file:s0
/data/ota_package(/.*)?		u:object_r:ota_package_file:s0
/data/unencrypted(/.*)?		u:object_r:unencrypted_data_file:s0
/data/misc/dhcp-6.8.2(/.*)?		u:object_r:dhcp_data_file:s0
/data/cache/backup(/.*)?		u:object_r:cache_private_backup_file:s0
/data/misc/profman(/.*)?		u:object_r:profman_dump_data_file:s0
/data/misc/wmtrace(/.*)?		u:object_r:wm_trace_data_file:s0
/data/nativetest64(/.*)?		u:object_r:nativetest_data_file:s0
/data/local/traces(/.*)?		u:object_r:trace_data_file:s0
/data/dalvik-cache(/.*)?		u:object_r:dalvikcache_data_file:s0
/dev/adf-interface[0-9]*\.[0-9]*		u:object_r:graphics_device:s0
/system/bin/dex2oat(d)?		u:object_r:dex2oat_exec:s0
/system/bin/profman(d)?		u:object_r:profman_exec:s0
/dev/socket/wpa_eth[0-9]		u:object_r:wpa_socket:s0
/cache/backup_stage(/.*)?		u:object_r:cache_backup_file:s0
/data/misc/zoneinfo(/.*)?		u:object_r:zoneinfo_data_file:s0
/data/misc/keychain(/.*)?		u:object_r:keychain_data_file:s0
/data/misc/keystore(/.*)?		u:object_r:keystore_data_file:s0
/data/misc/recovery(/.*)?		u:object_r:recovery_data_file:s0
/data/misc/bootstat(/.*)?		u:object_r:bootstat_data_file:s0
/data/misc/audiohal(/.*)?		u:object_r:audiohal_data_file:s0
/data/preloads/demo(/.*)?		u:object_r:preloads_media_file:s0
/data/local/tmp/ltp(/.*)?		u:object_r:nativetest_data_file:s0
/data/secure/backup(/.*)?		u:object_r:backup_data_file:s0
/data/system/users/[0-9]+/photo.png		u:object_r:icon_file:s0
/data/system/users/[0-9]+/wallpaper		u:object_r:wallpaper_file:s0
/data/system/users/[0-9]+/fpdata(/.*)?		u:object_r:fingerprintd_data_file:s0
/data/system/users/[0-9]+/wallpaper_lock		u:object_r:wallpaper_file:s0
/data/system/users/[0-9]+/wallpaper_orig		u:object_r:wallpaper_file:s0
/data/system/users/[0-9]+/wallpaper_lock_orig		u:object_r:wallpaper_file:s0
/system/bin/dhcpcd-6.8.2		u:object_r:dhcp_exec:s0
/system/bin/patchoat(d)?		u:object_r:dex2oat_exec:s0
/dev/socket/wpa_wlan[0-9]		u:object_r:wpa_socket:s0
/data/cache/recovery(/.*)?		u:object_r:cache_recovery_file:s0
/data/misc/perfprofd(/.*)?		u:object_r:perfprofd_data_file:s0
/data/misc/incidents(/.*)?		u:object_r:incident_data_file:s0
/data/misc/carrierid(/.*)?		u:object_r:radio_data_file:s0
/data/misc/bluetooth(/.*)?		u:object_r:bluetooth_data_file:s0
/data/misc/boottrace(/.*)?		u:object_r:boottrace_data_file:s0
/data/misc/bluedroid(/.*)?		u:object_r:bluetooth_data_file:s0
/data/preloads/media(/.*)?		u:object_r:preloads_media_file:s0
/data/resource-cache(/.*)?		u:object_r:resourcecache_data_file:s0
/system/bin/cppreopts.sh		u:object_r:cppreopts_exec:s0
/data/system/heapdump(/.*)?		u:object_r:heapdump_data_file:s0
/data/misc/systemkeys(/.*)?		u:object_r:systemkeys_data_file:s0
/data/misc/gatekeeper(/.*)?		u:object_r:gatekeeper_data_file:s0
/data/misc/stats-data(/.*)?		u:object_r:stats_data_file:s0
/data/misc/audioserver(/.*)?		u:object_r:audioserver_data_file:s0
/data/app-private/vmdl.*\.tmp(/.*)?		u:object_r:apk_private_tmp_file:s0
/data/misc/profiles/cur(/.*)?		u:object_r:user_profile_data_file:s0
/data/misc/profiles/ref(/.*)?		u:object_r:user_profile_data_file:s0
/data/misc/wifi/sockets(/.*)?		u:object_r:wpa_socket:s0
/data/misc/shared_relro(/.*)?		u:object_r:shared_relro_file:s0
/dev/adf-overlay-engine[0-9]*\.[0-9]*		u:object_r:graphics_device:s0
/data/cache/backup_stage(/.*)?		u:object_r:cache_backup_file:s0
/data/misc/update_engine(/.*)?		u:object_r:update_engine_data_file:s0
/data/misc/stats-service(/.*)?		u:object_r:stats_data_file:s0
/data/misc/textclassifier(/.*)?		u:object_r:textclassifier_data_file:s0
/data/misc/bluetooth/logs(/.*)?		u:object_r:bluetooth_logs_data_file:s0
/system/bin/dexoptanalyzer(d)?		u:object_r:dexoptanalyzer_exec:s0
/data/misc/perfetto-traces(/.*)?		u:object_r:perfetto_traces_data_file:s0
/system/bin/install-recovery.sh		u:object_r:install_recovery_exec:s0
/data/misc/update_engine_log(/.*)?		u:object_r:update_engine_log_data_file:s0
/data/misc/network_watchlist(/.*)?		u:object_r:network_watchlist_data_file:s0
/data/vendor/tombstones/wifi(/.*)?		u:object_r:tombstone_wifi_data_file:s0
/system/etc/selinux/mapping/[0-9]+\.[0-9]+\.cil		u:object_r:sepolicy_file:s0
/data/misc/wifi/sockets/wpa_ctrl.*		u:object_r:system_wpa_socket:s0
/system/etc/selinux/plat_sepolicy.cil		u:object_r:sepolicy_file:s0
/		u:object_r:rootfs:s0
/d		u:object_r:rootfs:s0
/etc		u:object_r:rootfs:s0
/mnt		u:object_r:tmpfs:s0
/sys		u:object_r:sysfs:s0
/bin		u:object_r:rootfs:s0
/proc		u:object_r:rootfs:s0
/init		u:object_r:init_exec:s0
/acct		u:object_r:cgroup:s0
/sdcard		u:object_r:rootfs:s0
/config		u:object_r:rootfs:s0
/dev/tun		u:object_r:tun_device:s0
/dev/tty		u:object_r:owntty_device:s0
/dev/eac		u:object_r:audio_device:s0
/dev/cam		u:object_r:camera_device:s0
/dev/ppp		u:object_r:ppp_device:s0
/dev/ion		u:object_r:ion_device:s0
/dev/mem		u:object_r:kmem_device:s0
/charger		u:object_r:rootfs:s0
/dev/zero		u:object_r:zero_device:s0
/dev/uhid		u:object_r:uhid_device:s0
/dev/full		u:object_r:full_device:s0
/dev/fuse		u:object_r:fuse_device:s0
/dev/ptmx		u:object_r:ptmx_device:s0
/dev/kmsg		u:object_r:kmsg_device:s0
/dev/port		u:object_r:port_device:s0
/dev/kmem		u:object_r:kmem_device:s0
/dev/null		u:object_r:null_device:s0
/sepolicy		u:object_r:sepolicy_file:s0
/adb_keys		u:object_r:adb_keys_file:s0
/dev/pmsg0		u:object_r:pmsg_device:s0
/dev/pn544		u:object_r:nfc_device:s0
/dev/alarm		u:object_r:alarm_device:s0
/dev/uinput		u:object_r:uhid_device:s0
/dev/random		u:object_r:random_device:s0
/dev/binder		u:object_r:binder_device:s0
/dev/ashmem		u:object_r:ashmem_device:s0
/bugreports		u:object_r:rootfs:s0
/verity_key		u:object_r:rootfs:s0
/lost\+found		u:object_r:rootfs:s0
/build\.prop		u:object_r:rootfs:s0
/dev/urandom		u:object_r:random_device:s0
/dev/console		u:object_r:console_device:s0
/dev/mtp_usb		u:object_r:mtp_device:s0
/dev/nvhdcp1		u:object_r:video_device:s0
/postinstall		u:object_r:postinstall_mnt_dir:s0
/dev/watchdog		u:object_r:watchdog_device:s0
/dev/hwbinder		u:object_r:hwbinder_device:s0
/dev/pvrsrvkm		u:object_r:gpu_device:s0
/dev/keychord		u:object_r:keychord_device:s0
/default\.prop		u:object_r:rootfs:s0
/dev/vndbinder		u:object_r:vndbinder_device:s0
/dev/hw_random		u:object_r:hw_random_device:s0
/system/bin/sh		--	u:object_r:shell_exec:s0
/system/xbin/su		u:object_r:su_exec:s0
/system/bin/vdc		u:object_r:vdc_exec:s0
/dev/xt_qtaguid		u:object_r:qtaguid_device:s0
/dev/kmsg_debug		u:object_r:kmsg_debug_device:s0
/dev/rproc_user		u:object_r:rpmsg_device:s0
/seapp_contexts		u:object_r:seapp_contexts_file:s0
/system/bin/vold		u:object_r:vold_exec:s0
/system/bin/netd		u:object_r:netd_exec:s0
/system/bin/mtpd		u:object_r:mtp_exec:s0
/system/bin/pppd		u:object_r:ppp_exec:s0
/system/bin/lmkd		u:object_r:lmkd_exec:s0
/system/bin/logd		u:object_r:logd_exec:s0
/system/bin/usbd		u:object_r:usbd_exec:s0
/dev/socket/rild		u:object_r:rild_socket:s0
/dev/socket/mdns		u:object_r:mdns_socket:s0
/dev/socket/mtpd		u:object_r:mtpd_socket:s0
/dev/socket/netd		u:object_r:netd_socket:s0
/dev/socket/lmkd		u:object_r:lmkd_socket:s0
/dev/socket/logd		u:object_r:logd_socket:s0
/dev/socket/adbd		u:object_r:adbd_socket:s0
/selinux_version		u:object_r:rootfs:s0
/system/bin/adbd		u:object_r:adbd_exec:s0
/system/bin/mdnsd		u:object_r:mdnsd_exec:s0
/system/bin/clatd		u:object_r:clatd_exec:s0
/system/bin/blkid		u:object_r:blkid_exec:s0
/system/bin/idmap		u:object_r:idmap_exec:s0
/dev/loop-control		u:object_r:loop_control_device:s0
/dev/socket/mdnsd		u:object_r:mdnsd_socket:s0
/dev/socket/logdr		u:object_r:logdr_socket:s0
/dev/socket/logdw		u:object_r:logdw_socket:s0
/system/bin/stats		u:object_r:stats_exec:s0
/system/bin/sdcard		u:object_r:sdcardd_exec:s0
/system/bin/racoon		u:object_r:racoon_exec:s0
/system/bin/dhcpcd		u:object_r:dhcp_exec:s0
/system/bin/traced		u:object_r:traced_exec:s0
/system/bin/sgdisk		u:object_r:sgdisk_exec:s0
/dev/socket/racoon		u:object_r:racoon_socket:s0
/dev/socket/zygote		u:object_r:zygote_socket:s0
/dev/usb_accessory		u:object_r:usbaccessory_device:s0
/system/bin/mke2fs		u:object_r:e2fs_exec:s0
/system/bin/atrace		u:object_r:atrace_exec:s0
/dev/device-mapper		u:object_r:dm_device:s0
/dev/accelerometer		u:object_r:sensors_device:s0
/plat_sepolicy\.cil		u:object_r:sepolicy_file:s0
/system/bin/statsd		u:object_r:statsd_exec:s0
/system/bin/vr_hwc		u:object_r:vr_hwc_exec:s0
/system/bin/run-as		--	u:object_r:runas_exec:s0
/system/bin/toybox		--	u:object_r:toolbox_exec:s0
/system/bin/logcat		--	u:object_r:logcat_exec:s0
/system/bin/e2fsck		--	u:object_r:fsck_exec:s0
/system/bin/dnsmasq		u:object_r:dnsmasq_exec:s0
/system/bin/healthd		u:object_r:healthd_exec:s0
/system/bin/uncrypt		u:object_r:uncrypt_exec:s0
/system/bin/bspatch		u:object_r:update_engine_exec:s0
/dev/socket/uncrypt		u:object_r:uncrypt_socket:s0
/dev/__properties__		u:object_r:properties_device:s0
/dev/event-log-tags		u:object_r:runtime_event_log_tags_file:s0
/dev/socket/statsdw		u:object_r:statsdw_socket:s0
/dev/socket/fwmarkd		u:object_r:fwmarkd_socket:s0
/plat_file_contexts		u:object_r:file_contexts_file:s0
/system/bin/logcatd		--	u:object_r:logcat_exec:s0
/system/bin/tune2fs		--	u:object_r:fsck_exec:s0
/system/bin/toolbox		--	u:object_r:toolbox_exec:s0
/system/bin/installd		u:object_r:installd_exec:s0
/system/bin/keystore		u:object_r:keystore_exec:s0
/system/bin/wificond		u:object_r:wificond_exec:s0
/system/bin/perfetto		u:object_r:perfetto_exec:s0
/system/bin/storaged		u:object_r:storaged_exec:s0
/system/bin/wpantund		u:object_r:wpantund_exec:s0
/system/bin/incident		u:object_r:incident_exec:s0
/system/bin/bootstat		u:object_r:bootstat_exec:s0
/plat_seapp_contexts		u:object_r:seapp_contexts_file:s0
/vndservice_contexts		u:object_r:vndservice_contexts_file:s0
/system/bin/perfprofd		u:object_r:perfprofd_exec:s0
/system/bin/drmserver		u:object_r:drmserver_exec:s0
/system/bin/dumpstate		u:object_r:dumpstate_exec:s0
/system/bin/incidentd		u:object_r:incidentd_exec:s0
/system/bin/e2fsdroid		u:object_r:e2fs_exec:s0
/dev/socket/dumpstate		u:object_r:dumpstate_socket:s0
/dev/socket/dnsproxyd		u:object_r:dnsproxyd_socket:s0
/mapping_sepolicy\.cil		u:object_r:sepolicy_file:s0
/nonplat_sepolicy\.cil		u:object_r:sepolicy_file:s0
/vendor_file_contexts		u:object_r:file_contexts_file:s0
/system/bin/bpfloader		u:object_r:bpfloader_exec:s0
/system/bin/fsck\.f2fs		--	u:object_r:fsck_exec:s0
/system/bin/make_f2fs		--	u:object_r:e2fs_exec:s0
/system/bin/tombstoned		u:object_r:tombstoned_exec:s0
/system/bin/logwrapper		u:object_r:system_file:s0
/dev/socket/rild-debug		u:object_r:rild_debug_socket:s0
/system/bin/bufferhubd		u:object_r:bufferhubd_exec:s0
/vendor_seapp_contexts		u:object_r:seapp_contexts_file:s0
/plat_service_contexts		u:object_r:service_contexts_file:s0
/nonplat_file_contexts		u:object_r:file_contexts_file:s0
/system/bin/fsck_msdos		--	u:object_r:fsck_exec:s0
/system/bin/sload_f2fs		--	u:object_r:e2fs_exec:s0
/system/bin/mediaserver		u:object_r:mediaserver_exec:s0
/system/bin/audioserver		u:object_r:audioserver_exec:s0
/system/bin/gatekeeperd		u:object_r:gatekeeperd_exec:s0
/system/bin/tzdatacheck		u:object_r:tzdatacheck_exec:s0
/plat_property_contexts		u:object_r:property_contexts_file:s0
/nonplat_seapp_contexts		u:object_r:seapp_contexts_file:s0
/system/bin/mediametrics		u:object_r:mediametrics_exec:s0
/system/bin/cameraserver		u:object_r:cameraserver_exec:s0
/system/bin/crash_dump32		u:object_r:crash_dump_exec:s0
/system/bin/crash_dump64		u:object_r:crash_dump_exec:s0
/system/bin/fingerprintd		u:object_r:fingerprintd_exec:s0
/system/bin/inputflinger		u:object_r:inputflinger_exec:s0
/system/bin/performanced		u:object_r:performanced_exec:s0
/system/bin/blank_screen		u:object_r:blank_screen_exec:s0
/vendor_service_contexts		u:object_r:nonplat_service_contexts_file:s0
/plat_hwservice_contexts		u:object_r:hwservice_contexts_file:s0
/system/bin/traced_probes		u:object_r:traced_probes_exec:s0
/system/bin/update_engine		u:object_r:update_engine_exec:s0
/system/bin/app_process64		u:object_r:zygote_exec:s0
/system/bin/app_process32		u:object_r:zygote_exec:s0
/system/bin/bootanimation		u:object_r:bootanim_exec:s0
/dev/snd/audio_seq_device		u:object_r:audio_seq_device:s0
/vendor_property_contexts		u:object_r:property_contexts_file:s0
/nonplat_service_contexts		u:object_r:nonplat_service_contexts_file:s0
/data/system/ndebugsocket		u:object_r:system_ndebug_socket:s0
/system/bin/screencontrol		u:object_r:screen_control_exec:s0
/system/bin/otapreopt_slot		u:object_r:otapreopt_slot_exec:s0
/system/bin/mediaextractor		u:object_r:mediaextractor_exec:s0
/system/bin/mediadrmserver		u:object_r:mediadrmserver_exec:s0
/system/bin/surfaceflinger		u:object_r:surfaceflinger_exec:s0
/system/bin/servicemanager		u:object_r:servicemanager_exec:s0
/nonplat_property_contexts		u:object_r:property_contexts_file:s0
/vendor_hwservice_contexts		u:object_r:hwservice_contexts_file:s0
/system/bin/incident_helper		u:object_r:incident_helper_exec:s0
/system/bin/update_verifier		u:object_r:update_verifier_exec:s0
/system/bin/thermalserviced		u:object_r:thermalserviced_exec:s0
/dev/socket/traced_producer		u:object_r:traced_producer_socket:s0
/dev/socket/traced_consumer		u:object_r:traced_consumer_socket:s0
/dev/snd/audio_timer_device		u:object_r:audio_timer_device:s0
/nonplat_hwservice_contexts		u:object_r:hwservice_contexts_file:s0
/system/bin/otapreopt_chroot		u:object_r:otapreopt_chroot_exec:s0
/system/bin/recovery-persist		u:object_r:recovery_persist_exec:s0
/system/bin/recovery-refresh		u:object_r:recovery_refresh_exec:s0
/system/bin/preopt2cachename		u:object_r:preopt2cachename_exec:s0
/system/bin/virtual_touchpad		u:object_r:virtual_touchpad_exec:s0
/dev/socket/tombstoned_crash		u:object_r:tombstoned_crash_socket:s0
/dev/socket/property_service		u:object_r:property_socket:s0
/dev/socket/zygote_secondary		u:object_r:zygote_socket:s0
/system/bin/hwservicemanager		u:object_r:hwservicemanager_exec:s0
/system/bin/wait_for_keymaster		u:object_r:wait_for_keymaster_exec:s0
/data/misc/bluedroid/\.a2dp_ctrl		u:object_r:bluetooth_socket:s0
/data/misc/bluedroid/\.a2dp_data		u:object_r:bluetooth_socket:s0
/system/bin/netutils-wrapper-1\.0		u:object_r:netutils_wrapper_exec:s0
/dev/socket/tombstoned_intercept		u:object_r:tombstoned_intercept_socket:s0
/system/bin/vold_prepare_subdirs		u:object_r:vold_prepare_subdirs_exec:s0
/dev/socket/tombstoned_java_trace		u:object_r:tombstoned_java_trace_socket:s0
/dev/__properties__/property_info		u:object_r:property_info:s0
/dev/socket/pdx/system/vr/display		u:object_r:pdx_display_dir:s0
/dev/socket/pdx/system/buffer_hub		u:object_r:pdx_bufferhub_dir:s0
/dev/socket/pdx/system/performance		u:object_r:pdx_performance_dir:s0
/odm/etc/selinux/precompiled_sepolicy		u:object_r:sepolicy_file:s0
/system/etc/selinux/plat_file_contexts		u:object_r:file_contexts_file:s0
/dev/socket/pdx/system/vr/display/vsync		u:object_r:pdx_display_vsync_endpoint_socket:s0
/system/etc/selinux/plat_seapp_contexts		u:object_r:seapp_contexts_file:s0
/dev/socket/pdx/system/vr/display/client		u:object_r:pdx_display_client_endpoint_socket:s0
/dev/socket/pdx/system/buffer_hub/client		u:object_r:pdx_bufferhub_client_endpoint_socket:s0
/dev/socket/pdx/system/vr/display/manager		u:object_r:pdx_display_manager_endpoint_socket:s0
/dev/socket/pdx/system/performance/client		u:object_r:pdx_performance_client_endpoint_socket:s0
/system/etc/selinux/plat_service_contexts		u:object_r:service_contexts_file:s0
/system/etc/selinux/plat_property_contexts		u:object_r:property_contexts_file:s0
/system/etc/selinux/plat_hwservice_contexts		u:object_r:hwservice_contexts_file:s0
/dev/socket/pdx/system/vr/display/screenshot		u:object_r:pdx_display_screenshot_endpoint_socket:s0
/system/etc/selinux/plat_mac_permissions\.xml		u:object_r:mac_perms_file:s0
/system/bin/hw/android\.hidl\.allocator@1\.0-service		u:object_r:hal_allocator_default_exec:s0
/system/etc/selinux/plat_and_mapping_sepolicy\.cil\.sha256		u:object_r:sepolicy_file:s0
/odm/etc/selinux/precompiled_sepolicy\.plat_and_mapping\.sha256		u:object_r:sepolicy_file:s0
