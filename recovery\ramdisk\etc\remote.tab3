#*********************************************************************************************************
#this file is used to store key map table
# custom_name:	  name of map table
# custom_code:	  custom code for remote device
# release_delay	  unit:ms.release will report from kernel to user layer after this period of time
#			  from press or repeat triggered.
###PARAS FOR MOUSE MODE:
# fn_key_scancode: scancode of fn key which used to swith mode
# cursor_left_scancode: scancode of left key
# cursor_right_scancode:  scancode of right key
# cursor_up_scancode: scancode of up key
# cursor_down_scancode: scancode of down key
# cursor_ok_scancode: scancode of ok key
#************************************************************************************************************* 
# <EMAIL>,20231109,Added the KEYCODE FACTORY POWER key one-touch shutdown function 
custom_name = amlogic-remote-4
custom_code = 0xba02
release_delay = 80

key_begin
    0x0b 579   #FACTORY_POWER
key_end
