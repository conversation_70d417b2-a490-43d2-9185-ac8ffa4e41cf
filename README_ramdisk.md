# Android Ramdisk CPIO 工具包

**Copyright 2025 By.举个🌰**

这是一个专门用于安全处理Android Recovery ramdisk.cpio文件的工具包，支持解包、修改和重新打包，同时保护原始文件不被损坏。

## 📁 项目结构

```
├── recovery/
│   ├── ramdisk.cpio          # 原始CPIO文件
│   └── ramdisk/              # 已解包的文件系统
├── ramdisk_tools.py          # Python工具脚本
├── ramdisk_manager.bat       # Windows批处理管理器
├── README_ramdisk.md         # 本说明文档
├── backup/                   # 备份目录（自动创建）
└── ramdisk_extracted/        # 解包目录（自动创建）
```

## 🛠️ 工具说明

### 1. ramdisk_manager.bat (推荐)
Windows批处理脚本，提供图形化菜单界面，操作简单直观。

**功能特性:**
- ✅ 自动备份原始文件
- ✅ 多种解包方式（7-zip、WinRAR、Python）
- ✅ 安全的重新打包
- ✅ 文件完整性验证
- ✅ 备份管理功能

### 2. ramdisk_tools.py
Python脚本，提供命令行接口，功能更加强大。

**功能特性:**
- ✅ MD5哈希验证
- ✅ 自动备份机制
- ✅ 跨平台支持
- ✅ 详细的错误处理

## 🚀 快速开始

### 方法一：使用批处理工具（推荐新手）

1. **双击运行** `ramdisk_manager.bat`
2. **选择操作**：
   - `[1]` 解包CPIO文件
   - `[2]` 重新打包为CPIO
   - `[3]` 验证文件完整性
   - `[4]` 创建备份
   - `[5]` 恢复备份

### 方法二：使用Python脚本（推荐高级用户）

```bash
# 解包CPIO文件
python ramdisk_tools.py extract -i recovery/ramdisk.cpio -o ramdisk_extracted

# 重新打包
python ramdisk_tools.py create -i ramdisk_extracted -o new_ramdisk.cpio

# 验证完整性
python ramdisk_tools.py verify -i ramdisk_extracted --original recovery/ramdisk
```

## 📋 详细操作步骤

### 1. 解包ramdisk.cpio

**使用批处理工具:**
1. 运行 `ramdisk_manager.bat`
2. 选择 `[1] 解包 CPIO 文件`
3. 工具会自动尝试多种解包方式
4. 解包完成后，文件会保存在 `ramdisk_extracted/` 目录

**使用Python脚本:**
```bash
python ramdisk_tools.py extract -i recovery/ramdisk.cpio -o ramdisk_extracted
```

### 2. 修改文件系统

解包完成后，您可以在 `ramdisk_extracted/` 目录中修改文件：

- **修改配置文件**: 如 `init.rc`, `prop.default` 等
- **添加/删除文件**: 根据需要修改文件系统结构
- **修改权限**: 注意保持正确的文件权限

### 3. 重新打包

**使用批处理工具:**
1. 运行 `ramdisk_manager.bat`
2. 选择 `[2] 重新打包为 CPIO`
3. 选择是否替换原始文件

**使用Python脚本:**
```bash
python ramdisk_tools.py create -i ramdisk_extracted -o new_ramdisk.cpio
```

### 4. 验证完整性

```bash
python ramdisk_tools.py verify -i ramdisk_extracted --original recovery/ramdisk
```

## ⚙️ 系统要求

### Windows环境
- Windows 10/11
- PowerShell 5.0+
- 推荐安装以下工具之一：
  - **7-Zip** (推荐)
  - **WinRAR**
  - **Python 3.6+**

### 安装依赖工具

**安装7-Zip (推荐):**
1. 下载: https://www.7-zip.org/
2. 安装后确保 `7z.exe` 在系统PATH中

**安装Python (可选):**
1. 下载: https://www.python.org/
2. 安装时勾选 "Add Python to PATH"

## 🔒 安全特性

### 自动备份机制
- 每次操作前自动创建原始文件备份
- 备份文件带时间戳，避免覆盖
- 支持手动备份和恢复功能

### 文件完整性保护
- MD5哈希验证确保文件完整性
- 操作前检查文件存在性
- 错误处理机制防止数据丢失

### 多重解包方式
- 优先使用7-Zip（兼容性最好）
- 备选WinRAR和Python方式
- 自动选择最佳解包方法

## 📝 常见问题

### Q: 解包失败怎么办？
A: 工具会自动尝试多种解包方式。如果都失败，请：
1. 确保安装了7-Zip或WinRAR
2. 检查CPIO文件是否损坏
3. 尝试使用Python脚本

### Q: 如何恢复原始文件？
A: 使用批处理工具的 `[5] 恢复备份` 功能，或手动从 `backup/` 目录复制备份文件。

### Q: 修改后的文件系统如何验证？
A: 使用验证功能比较原始目录和解包目录的文件数量和结构。

### Q: 支持哪些CPIO格式？
A: 支持标准的newc格式CPIO文件，这是Android常用的格式。

## 🔧 高级用法

### 批量处理
```bash
# 处理多个CPIO文件
for file in *.cpio; do
    python ramdisk_tools.py extract -i "$file" -o "${file%.cpio}_extracted"
done
```

### 自定义输出目录
```bash
python ramdisk_tools.py extract -i recovery/ramdisk.cpio -o custom_output_dir
```

### 验证哈希值
```bash
# 工具会自动显示原始文件和新文件的MD5哈希值
python ramdisk_tools.py create -i ramdisk_extracted -o new_ramdisk.cpio
```

## 📞 技术支持

如果遇到问题，请检查：
1. 系统是否满足要求
2. 依赖工具是否正确安装
3. 文件路径是否正确
4. 是否有足够的磁盘空间

---

**Copyright 2025 By.举个🌰**

*本工具专为Android固件逆向工程设计，请合法使用。*
