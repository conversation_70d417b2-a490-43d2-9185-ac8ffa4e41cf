#line 1 "device/amlogic/common/sepolicy/property_contexts"
media.                  u:object_r:media_prop:s0
ro.media.               u:object_r:media_prop:s0
sys.media.              u:object_r:media_prop:s0
sys.subtitle.           u:object_r:media_prop:s0
ro.audio.               u:object_r:media_prop:s0
ro.af.                  u:object_r:media_prop:s0
persist.vendor.audio.   u:object_r:media_prop:s0
persist.vendor.media.   u:object_r:media_prop:s0
persist.vendor.bt_vendor       u:object_r:vendor_platform_prop:s0
persist.vendor.wifi_type       u:object_r:vendor_platform_prop:s0
persist.vendor.wifi_remove     u:object_r:vendor_platform_prop:s0
drm.                    u:object_r:media_prop:s0
iptv.middle.softdemux   u:object_r:media_prop:s0
audio.                  u:object_r:audio_prop:s0
ubootenv.               u:object_r:uboot_prop:s0
ro.ubootenv.            u:object_r:uboot_prop:s0
snd.                    u:object_r:tv_config_prop:s0
tv.                     u:object_r:tv_prop:s0
persist.tv.             u:object_r:tv_prop:s0
bcmdl_status            u:object_r:bcmdl_prop:s0
wc_transport            u:object_r:bluetooth_prop:s0
rc_hidraw_fd            u:object_r:bluetooth_prop:s0
ro.rfkilldisabled      u:object_r:bluetooth_prop:s0
vendor.display-size     u:object_r:netflix_prop:s0
persist.vendor.hwc.keystone    u:object_r:vendor_persist_prop:s0
persist.vendor.hwc.keystone.debug    u:object_r:vendor_persist_prop:s0
ro.vendor.nrdp.modelgroup     u:object_r:netflix_prop:s0
ro.vendor.platform      u:object_r:vendor_platform_prop:s0
persist.vendor.sys      u:object_r:vendor_persist_prop:s0
vendor.sys              u:object_r:vendor_platform_prop:s0
vendor.filesystem.      u:object_r:vendor_platform_prop:s0
ro.vendor.app           u:object_r:vendor_app_prop:s0
ro.net.pppoe		    u:object_r:net_pppoe_prop:s0
persist.miracast.hdcp2  u:object_r:miracast_prop:s0
#line 1 "system/sepolicy/reqd_mask/property_contexts"
# empty property_contexts file - this file is used to generate an empty
# non-platform property context for devices without any property_contexts
# customizations.
