#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ramdisk工具启动器
Copyright 2025 By.举个🌰

自动选择最佳的工具界面启动
"""

import sys
import subprocess
from pathlib import Path

def check_tkinter():
    """检查tkinter是否可用"""
    try:
        import tkinter
        return True
    except ImportError:
        return False

def start_gui():
    """启动GUI版本"""
    try:
        import ramdisk_gui
        app = ramdisk_gui.RamdiskGUI()
        app.run()
        return True
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return False

def start_cli():
    """启动CLI版本"""
    try:
        import ramdisk_cli
        app = ramdisk_cli.RamdiskCLI()
        app.run()
        return True
    except Exception as e:
        print(f"❌ CLI启动失败: {e}")
        return False

def main():
    print("=" * 60)
    print("Android Ramdisk CPIO 工具启动器")
    print("Copyright 2025 By.举个🌰")
    print("=" * 60)
    print()
    
    # 检查当前目录是否有ramdisk.cpio文件
    cpio_file = Path("recovery/ramdisk.cpio")
    if cpio_file.exists():
        print(f"✓ 检测到CPIO文件: {cpio_file}")
    else:
        print(f"⚠️  未检测到CPIO文件: {cpio_file}")
        print("   请确保在正确的目录中运行此工具")
    print()
    
    # 选择界面类型
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['gui', 'g']:
            print("🖥️  启动图形界面...")
            if check_tkinter():
                if start_gui():
                    return
            print("❌ 图形界面启动失败，切换到命令行界面")
            
        elif sys.argv[1].lower() in ['cli', 'c']:
            print("💻 启动命令行界面...")
            start_cli()
            return
    
    # 自动选择最佳界面
    print("🔍 自动检测最佳界面...")
    
    if check_tkinter():
        print("✓ 检测到tkinter支持，启动图形界面...")
        if start_gui():
            return
        print("❌ 图形界面启动失败，切换到命令行界面")
    
    print("💻 启动命令行界面...")
    start_cli()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
