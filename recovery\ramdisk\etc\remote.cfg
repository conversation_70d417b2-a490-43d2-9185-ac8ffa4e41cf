#*********************************************************************************************************
#this file is used to config the remote driver
# work_mode:          set work mode for IR, refer to the include/dt-bindings/input/meson_rc.h
# debug_enable:       0 :disable    1:enable
# repeat_enable:      0 :disable    1:enable
#
# SW MODE:
#   max_frame_time  maximum frame time

#*************************************************************************************************************
work_mode = 0x1
repeat_enable = 0
debug_enable = 0

#sw decode parameters
max_frame_time = 1000
