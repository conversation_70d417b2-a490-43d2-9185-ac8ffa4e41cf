#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯Python CPIO文件处理器
Copyright 2025 By.举个🌰

实现CPIO文件的解包和打包功能，不依赖外部工具
"""

import os
import struct
import stat
from pathlib import Path

class CPIOHandler:
    """CPIO文件处理器"""
    
    # CPIO newc格式的魔数
    CPIO_NEWC_MAGIC = b'070701'
    CPIO_NEWC_CRC_MAGIC = b'070702'
    TRAILER = "TRAILER!!!"
    
    def __init__(self):
        self.verbose = True
        
    def log(self, message):
        """输出日志"""
        if self.verbose:
            print(message)
            
    def parse_header(self, data, offset):
        """解析CPIO头部"""
        if offset + 110 > len(data):
            return None, offset
            
        # 检查魔数
        magic = data[offset:offset+6]
        if magic not in [self.CPIO_NEWC_MAGIC, self.CPIO_NEWC_CRC_MAGIC]:
            return None, offset
            
        # 解析头部字段 (所有字段都是8位十六进制ASCII)
        header_format = "6s8s8s8s8s8s8s8s8s8s8s8s8s8s"
        header_size = struct.calcsize(header_format)
        
        if offset + header_size > len(data):
            return None, offset
            
        header_data = struct.unpack(header_format, data[offset:offset+header_size])
        
        try:
            header = {
                'magic': header_data[0],
                'ino': int(header_data[1], 16),
                'mode': int(header_data[2], 16),
                'uid': int(header_data[3], 16),
                'gid': int(header_data[4], 16),
                'nlink': int(header_data[5], 16),
                'mtime': int(header_data[6], 16),
                'filesize': int(header_data[7], 16),
                'devmajor': int(header_data[8], 16),
                'devminor': int(header_data[9], 16),
                'rdevmajor': int(header_data[10], 16),
                'rdevminor': int(header_data[11], 16),
                'namesize': int(header_data[12], 16),
                'check': int(header_data[13], 16)
            }
        except ValueError as e:
            self.log(f"❌ 解析头部失败: {e}")
            return None, offset
            
        return header, offset + header_size
        
    def align_4(self, offset):
        """4字节对齐"""
        return (offset + 3) & ~3
        
    def extract_cpio(self, cpio_file, extract_dir):
        """解包CPIO文件"""
        cpio_path = Path(cpio_file)
        extract_path = Path(extract_dir)
        
        if not cpio_path.exists():
            self.log(f"❌ CPIO文件不存在: {cpio_path}")
            return False
            
        self.log(f"📦 开始解包: {cpio_path}")
        
        # 创建解包目录
        extract_path.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(cpio_path, 'rb') as f:
                data = f.read()
                
            offset = 0
            file_count = 0
            
            while offset < len(data):
                # 解析头部
                header, new_offset = self.parse_header(data, offset)
                if header is None:
                    break
                    
                offset = new_offset
                
                # 读取文件名
                if offset + header['namesize'] > len(data):
                    break
                    
                filename = data[offset:offset+header['namesize']-1].decode('utf-8', errors='ignore')
                offset = self.align_4(offset + header['namesize'])
                
                # 检查是否是结束标记
                if filename == self.TRAILER:
                    self.log("✓ 到达文件结束标记")
                    break
                    
                # 跳过当前目录标记
                if filename == '.':
                    offset = self.align_4(offset + header['filesize'])
                    continue
                    
                self.log(f"  处理: {filename}")
                
                # 创建文件路径
                file_path = extract_path / filename
                
                try:
                    # 创建父目录
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 根据文件类型处理
                    mode = header['mode']
                    
                    if stat.S_ISDIR(mode):
                        # 目录
                        file_path.mkdir(exist_ok=True)
                        self.log(f"    📁 创建目录: {filename}")
                        
                    elif stat.S_ISREG(mode):
                        # 普通文件
                        if offset + header['filesize'] > len(data):
                            self.log(f"    ⚠️ 文件数据不完整: {filename}")
                            break
                            
                        file_data = data[offset:offset+header['filesize']]
                        with open(file_path, 'wb') as out_file:
                            out_file.write(file_data)
                        self.log(f"    📄 创建文件: {filename} ({header['filesize']} 字节)")
                        
                    elif stat.S_ISLNK(mode):
                        # 符号链接
                        if offset + header['filesize'] > len(data):
                            self.log(f"    ⚠️ 链接数据不完整: {filename}")
                            break
                            
                        link_target = data[offset:offset+header['filesize']].decode('utf-8', errors='ignore')
                        
                        # 在Windows上创建符号链接可能失败，创建一个文本文件记录链接信息
                        try:
                            if os.name == 'nt':  # Windows
                                link_info_file = file_path.with_suffix(file_path.suffix + '.symlink')
                                with open(link_info_file, 'w', encoding='utf-8') as f:
                                    f.write(f"SYMLINK_TARGET: {link_target}\n")
                                self.log(f"    🔗 创建链接信息文件: {filename}.symlink -> {link_target}")
                            else:
                                os.symlink(link_target, file_path)
                                self.log(f"    🔗 创建符号链接: {filename} -> {link_target}")
                        except OSError as e:
                            self.log(f"    ⚠️ 创建符号链接失败: {filename} -> {link_target} ({e})")
                            
                    else:
                        # 其他类型文件（设备文件等）
                        self.log(f"    ❓ 跳过特殊文件: {filename} (mode: {oct(mode)})")
                        
                    file_count += 1
                    
                except Exception as e:
                    self.log(f"    ❌ 处理文件失败: {filename} - {e}")
                    
                # 移动到下一个文件
                offset = self.align_4(offset + header['filesize'])
                
            self.log(f"✓ 解包完成! 处理了 {file_count} 个文件")
            self.log(f"📁 文件保存在: {extract_path}")
            return True
            
        except Exception as e:
            self.log(f"❌ 解包过程出错: {e}")
            return False
            
    def create_cpio(self, source_dir, output_file):
        """创建CPIO文件"""
        source_path = Path(source_dir)
        output_path = Path(output_file)
        
        if not source_path.exists():
            self.log(f"❌ 源目录不存在: {source_path}")
            return False
            
        self.log(f"📦 开始打包: {source_path}")
        
        try:
            with open(output_path, 'wb') as f:
                # 收集所有文件
                files = []
                
                # 添加当前目录
                files.append(('.',  source_path))
                
                # 递归收集所有文件和目录
                for item in source_path.rglob('*'):
                    rel_path = item.relative_to(source_path)
                    files.append((str(rel_path).replace('\\', '/'), item))
                
                # 写入文件
                inode = 1
                for filename, file_path in files:
                    try:
                        stat_info = file_path.stat()
                        
                        # 构建头部
                        header = self._build_header(
                            inode=inode,
                            mode=stat_info.st_mode,
                            uid=0,  # 在Android中通常使用0
                            gid=0,
                            nlink=1,
                            mtime=int(stat_info.st_mtime),
                            filesize=stat_info.st_size if file_path.is_file() else 0,
                            namesize=len(filename) + 1
                        )
                        
                        # 写入头部
                        f.write(header)
                        
                        # 写入文件名
                        f.write(filename.encode('utf-8') + b'\0')
                        
                        # 4字节对齐
                        self._write_padding(f, len(filename) + 1)
                        
                        # 写入文件内容
                        if file_path.is_file():
                            with open(file_path, 'rb') as src_file:
                                content = src_file.read()
                                f.write(content)
                                self._write_padding(f, len(content))
                                
                        self.log(f"  添加: {filename}")
                        inode += 1
                        
                    except Exception as e:
                        self.log(f"  ⚠️ 跳过文件: {filename} - {e}")
                        continue
                
                # 写入结束标记
                trailer_header = self._build_header(
                    inode=0,
                    mode=0,
                    uid=0,
                    gid=0,
                    nlink=1,
                    mtime=0,
                    filesize=0,
                    namesize=len(self.TRAILER) + 1
                )
                
                f.write(trailer_header)
                f.write(self.TRAILER.encode('utf-8') + b'\0')
                self._write_padding(f, len(self.TRAILER) + 1)
                
            self.log(f"✓ 打包完成! 文件保存为: {output_path}")
            return True
            
        except Exception as e:
            self.log(f"❌ 打包过程出错: {e}")
            return False
            
    def _build_header(self, inode, mode, uid, gid, nlink, mtime, filesize, namesize):
        """构建CPIO头部"""
        header = (
            self.CPIO_NEWC_MAGIC +
            f"{inode:08x}".encode('ascii') +
            f"{mode:08x}".encode('ascii') +
            f"{uid:08x}".encode('ascii') +
            f"{gid:08x}".encode('ascii') +
            f"{nlink:08x}".encode('ascii') +
            f"{mtime:08x}".encode('ascii') +
            f"{filesize:08x}".encode('ascii') +
            f"{0:08x}".encode('ascii') +  # devmajor
            f"{0:08x}".encode('ascii') +  # devminor
            f"{0:08x}".encode('ascii') +  # rdevmajor
            f"{0:08x}".encode('ascii') +  # rdevminor
            f"{namesize:08x}".encode('ascii') +
            f"{0:08x}".encode('ascii')    # check
        )
        return header
        
    def _write_padding(self, f, size):
        """写入4字节对齐的填充"""
        padding = (4 - (size % 4)) % 4
        if padding > 0:
            f.write(b'\0' * padding)

# 测试函数
def test_cpio_handler():
    """测试CPIO处理器"""
    handler = CPIOHandler()
    
    # 测试解包
    cpio_file = "recovery/ramdisk.cpio"
    extract_dir = "ramdisk_python_extracted"
    
    if Path(cpio_file).exists():
        print("测试解包...")
        success = handler.extract_cpio(cpio_file, extract_dir)
        if success:
            print("✓ 解包测试成功")
        else:
            print("❌ 解包测试失败")
    else:
        print(f"❌ 测试文件不存在: {cpio_file}")

if __name__ == '__main__':
    test_cpio_handler()
