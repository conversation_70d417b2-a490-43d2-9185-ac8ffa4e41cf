import /init.recovery.${ro.hardware}.rc

on early-init
    # Set the security context of /postinstall if present.
    restorecon /postinstall

    start ueventd

on init
    export ANDROID_ROOT /system
    export ANDROID_DATA /data
    export EXTERNAL_STORAGE /sdcard

    symlink /system/bin /bin
    symlink /system/etc /etc

    mount cgroup none /acct cpuacct
    mkdir /acct/uid

    mkdir /sdcard
    mkdir /system
    mkdir /data
    mkdir /cache
    mkdir /sideload
    mount tmpfs tmpfs /tmp

    chown root shell /tmp
    chmod 0775 /tmp

    write /proc/sys/vm/watermark_scale_factor 30
    write /proc/sys/vm/min_free_kbytes 12288
    write /proc/sys/kernel/panic_on_oops 1
    write /proc/sys/vm/max_map_count 1000000

on fs
    write /sys/class/android_usb/android0/f_ffs/aliases adb
    mkdir /dev/usb-ffs 0770 shell shell
    mkdir /dev/usb-ffs/adb 0770 shell shell
    mount functionfs adb /dev/usb-ffs/adb uid=2000,gid=2000

    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18D1
    write /sys/class/android_usb/android0/idProduct D001
    write /sys/class/android_usb/android0/functions adb
    write /sys/class/android_usb/android0/iManufacturer ${ro.product.manufacturer}
    write /sys/class/android_usb/android0/iProduct ${ro.product.model}
    write /sys/class/android_usb/android0/iSerial ${ro.serialno}

on boot
    ifup lo
    hostname localhost
    domainname localdomain

    class_start default

# Load properties from /system/ + /factory after fs mount.
on load_system_props_action
    load_system_props

on firmware_mounts_complete
   rm /dev/.booting

# Mount filesystems and start core system services.
on late-init
    trigger early-fs
    trigger fs
    trigger post-fs
    trigger post-fs-data

    # Load properties from /system/ + /factory after fs mount. Place
    # this in another action so that the load will be scheduled after the prior
    # issued fs triggers have completed.
    trigger load_system_props_action

    # Remove a file to wake up anything waiting for firmware
    trigger firmware_mounts_complete

    trigger early-boot
    trigger boot

service ueventd /sbin/ueventd
    critical
    seclabel u:r:ueventd:s0

service charger /charger -r
    critical
    seclabel u:r:charger:s0

service recovery /sbin/recovery
    seclabel u:r:recovery:s0

service adbd /sbin/adbd --root_seclabel=u:r:su:s0 --device_banner=recovery
    disabled
    socket adbd stream 660 system system
    seclabel u:r:adbd:s0

# Always start adbd on userdebug and eng builds
on property:ro.debuggable=1
    write /sys/class/android_usb/android0/enable 1
    start adbd

# Restart adbd so it can run as root
on property:service.adb.root=1
    write /sys/class/android_usb/android0/enable 0
    restart adbd
    write /sys/class/android_usb/android0/enable 1
