#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ramdisk.cpio文件的基本信息
Copyright 2025 By.举个🌰
"""

import os
import sys
from pathlib import Path

def test_file_info():
    """测试文件基本信息"""
    print("=" * 60)
    print("Ramdisk CPIO 文件分析")
    print("Copyright 2025 By.举个🌰")
    print("=" * 60)
    
    cpio_file = Path("recovery/ramdisk.cpio")
    ramdisk_dir = Path("recovery/ramdisk")
    
    print(f"\n📋 文件信息分析:")
    print(f"当前工作目录: {Path.cwd()}")
    
    # 检查CPIO文件
    if cpio_file.exists():
        stat = cpio_file.stat()
        print(f"✓ CPIO文件: {cpio_file}")
        print(f"  文件大小: {stat.st_size:,} 字节 ({stat.st_size/1024/1024:.2f} MB)")
        print(f"  修改时间: {stat.st_mtime}")
        
        # 读取文件头部信息
        try:
            with open(cpio_file, 'rb') as f:
                header = f.read(16)
                print(f"  文件头部: {header.hex()}")
                
                # 检查是否是标准CPIO格式
                if header.startswith(b'070701') or header.startswith(b'070702'):
                    print(f"  ✓ 检测到标准CPIO newc格式")
                elif header.startswith(b'\x1f\x8b'):
                    print(f"  ✓ 检测到GZIP压缩格式")
                else:
                    print(f"  ⚠️  未知格式，可能需要特殊处理")
        except Exception as e:
            print(f"  ❌ 读取文件头部失败: {e}")
    else:
        print(f"❌ CPIO文件不存在: {cpio_file}")
    
    # 检查已解包目录
    if ramdisk_dir.exists():
        print(f"\n✓ 已解包目录: {ramdisk_dir}")
        
        # 统计文件数量 (安全处理符号链接)
        files = []
        dirs = []
        regular_files = []
        symlinks = []

        try:
            for item in ramdisk_dir.rglob('*'):
                try:
                    files.append(item)
                    if item.is_symlink():
                        symlinks.append(item)
                    elif item.is_dir():
                        dirs.append(item)
                    elif item.is_file():
                        regular_files.append(item)
                except (OSError, PermissionError):
                    # 跳过无法访问的文件（如损坏的符号链接）
                    continue
        except Exception as e:
            print(f"  ⚠️  遍历目录时出现问题: {e}")
        
        print(f"  总项目数: {len(files)}")
        print(f"  目录数: {len(dirs)}")
        print(f"  文件数: {len(regular_files)}")
        print(f"  符号链接数: {len(symlinks)}")
        
        # 显示主要目录结构
        print(f"\n📁 主要目录结构:")
        try:
            for item in sorted(ramdisk_dir.iterdir()):
                try:
                    if item.is_symlink():
                        print(f"  🔗 {item.name} -> (符号链接)")
                    elif item.is_dir():
                        print(f"  📁 {item.name}/")
                    else:
                        print(f"  📄 {item.name}")
                except (OSError, PermissionError):
                    print(f"  ❓ {item.name} (无法访问)")
        except Exception as e:
            print(f"  ⚠️  无法列出目录内容: {e}")
                
        # 显示一些重要文件
        important_files = ['init', 'init.rc', 'sepolicy', 'prop.default']
        print(f"\n📄 重要文件检查:")
        for filename in important_files:
            file_path = ramdisk_dir / filename
            try:
                if file_path.exists():
                    if file_path.is_symlink():
                        print(f"  🔗 {filename} (符号链接)")
                    elif file_path.is_file():
                        size = file_path.stat().st_size
                        print(f"  ✓ {filename} ({size} 字节)")
                    else:
                        print(f"  ✓ {filename} (目录)")
                else:
                    print(f"  ❌ {filename} (不存在)")
            except (OSError, PermissionError):
                print(f"  ❓ {filename} (无法访问)")
    else:
        print(f"❌ 已解包目录不存在: {ramdisk_dir}")
    
    print(f"\n🔧 推荐操作:")
    if cpio_file.exists() and not ramdisk_dir.exists():
        print("  1. 运行 ramdisk_manager.bat 选择 [1] 解包CPIO文件")
        print("  2. 或使用: python ramdisk_tools.py extract -i recovery/ramdisk.cpio")
    elif cpio_file.exists() and ramdisk_dir.exists():
        print("  1. 可以直接修改 recovery/ramdisk/ 目录中的文件")
        print("  2. 修改完成后运行 ramdisk_manager.bat 选择 [2] 重新打包")
        print("  3. 或使用: python ramdisk_tools.py create -i recovery/ramdisk -o new_ramdisk.cpio")
    else:
        print("  请检查文件路径是否正确")
    
    print(f"\n💡 提示:")
    print("  - 修改前请务必备份原始文件")
    print("  - 建议使用 ramdisk_manager.bat 进行操作，更加安全")
    print("  - 修改系统文件时要特别小心权限设置")

if __name__ == '__main__':
    test_file_info()
