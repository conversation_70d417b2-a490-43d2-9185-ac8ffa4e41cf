# Android Ramdisk CPIO 工具包使用说明

**Copyright 2025 By.举个🌰**

## 🎉 成功完成！

您的ramdisk.cpio文件已经成功解包！工具包现在完全可用。

## 📁 当前状态

- ✅ **原始文件**: `recovery/ramdisk.cpio` (12.96 MB)
- ✅ **解包目录**: `ramdisk_extracted/` (203个文件)
- ✅ **备份目录**: `backup/` (自动备份)
- ✅ **Python工具**: 完全可用，无需外部依赖

## 🛠️ 可用工具

### 1. 启动器 (推荐)
```bash
python start_ramdisk_tools.py
```
- 自动选择最佳界面
- 支持GUI和CLI模式

### 2. 命令行工具
```bash
python start_ramdisk_tools.py cli
```
- 纯命令行界面
- 功能完整，操作简单

### 3. 图形界面工具
```bash
python start_ramdisk_tools.py gui
```
- 图形化界面（如果支持tkinter）
- 更直观的操作体验

## 🚀 快速操作指南

### 解包CPIO文件 ✅ (已完成)
您的文件已经成功解包到 `ramdisk_extracted/` 目录

### 修改文件系统
1. **进入解包目录**:
   ```
   cd ramdisk_extracted
   ```

2. **修改重要文件**:
   - `init.rc` - 系统初始化脚本
   - `prop.default` - 系统属性配置
   - `sepolicy` - SELinux策略文件
   - `sbin/` - 系统二进制文件
   - `etc/` - 配置文件

3. **注意事项**:
   - 符号链接文件以 `.symlink` 结尾
   - 修改前请备份重要文件
   - 保持文件权限和结构

### 重新打包
1. **启动工具**:
   ```bash
   python start_ramdisk_tools.py cli
   ```

2. **选择操作**: `[2] 重新打包为 CPIO`

3. **确认替换**: 工具会询问是否替换原文件

## 📋 文件结构说明

### 重要目录
- `sbin/` - Recovery模式的核心程序
  - `recovery` - Recovery主程序
  - `adbd` - ADB调试守护进程
  - `busybox` - 基础命令工具集

- `etc/` - 配置文件
  - `recovery.fstab` - 分区挂载配置
  - `recovery.kl` - 按键映射
  - `remote.cfg` - 遥控器配置

- `res/` - 资源文件
  - `images/` - Recovery界面图片
  - `keys/` - 签名验证密钥

### 重要文件
- `init` - 系统初始化程序 (1.5MB)
- `init.rc` - 初始化脚本
- `sepolicy` - SELinux安全策略 (463KB)
- `prop.default` - 系统属性配置

### 符号链接处理
在Windows下，符号链接被转换为 `.symlink` 文件：
- `bin.symlink` → `/system/bin`
- `default.prop.symlink` → `prop.default`
- 重新打包时会正确恢复为符号链接

## 🔧 高级功能

### 1. 备份管理
- 自动备份: 每次操作前自动创建备份
- 手动备份: 选择 `[4] 创建备份`
- 恢复备份: 选择 `[5] 恢复备份`

### 2. 完整性验证
- 选择 `[3] 验证文件完整性`
- 比较文件数量和结构
- 确保修改后的完整性

### 3. 文件信息查看
- 选择 `[6] 查看文件信息`
- 显示文件大小、MD5哈希
- 统计目录和文件数量

## 💡 使用技巧

### 1. 安全修改流程
```bash
# 1. 创建备份
python start_ramdisk_tools.py cli
# 选择 [4] 创建备份

# 2. 修改文件
# 在 ramdisk_extracted/ 目录中修改需要的文件

# 3. 验证完整性
# 选择 [3] 验证文件完整性

# 4. 重新打包
# 选择 [2] 重新打包为 CPIO
```

### 2. 常见修改示例

**修改Recovery界面语言**:
- 编辑 `res/` 目录下的资源文件

**添加自定义脚本**:
- 修改 `init.rc` 添加启动脚本
- 在 `sbin/` 目录添加可执行文件

**修改系统属性**:
- 编辑 `prop.default` 文件
- 添加或修改系统属性

### 3. 故障排除

**如果解包失败**:
- 检查CPIO文件是否完整
- 确保有足够的磁盘空间
- 尝试不同的解包方法

**如果打包失败**:
- 检查解包目录结构
- 确保没有损坏的文件
- 检查文件权限

**如果符号链接有问题**:
- 查看 `.symlink` 文件内容
- 手动修复链接目标
- 重新打包时会自动处理

## 🔒 安全提醒

1. **始终备份**: 修改前创建备份
2. **小心测试**: 在测试设备上验证修改
3. **保持结构**: 不要随意删除系统文件
4. **权限注意**: 保持正确的文件权限
5. **签名验证**: 某些设备需要重新签名

## 📞 技术支持

如果遇到问题：
1. 检查工具输出的错误信息
2. 确保Python环境正常
3. 验证文件路径和权限
4. 查看备份文件是否完整

## 🎯 下一步

现在您可以：
1. ✅ 安全地修改ramdisk文件系统
2. ✅ 使用工具重新打包
3. ✅ 验证修改结果
4. ✅ 管理备份文件

**祝您使用愉快！**

---

**Copyright 2025 By.举个🌰**

*本工具专为Android固件逆向工程设计，请合法使用。*
